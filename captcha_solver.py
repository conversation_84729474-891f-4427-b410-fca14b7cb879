import cv2
import numpy as np
import base64
from io import BytesIO
from PIL import Image
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CaptchaSolver:
    """
    A class to solve slider captchas by detecting the position of the puzzle piece
    """

    def __init__(self):
        self.debug_mode = False
        self.debug_images = {}

    def enable_debug(self, enable=True):
        """Enable or disable debug mode to save intermediate images"""
        self.debug_mode = enable

    def _save_debug_image(self, name, image):
        """Save an image for debugging purposes"""
        if self.debug_mode:
            self.debug_images[name] = image

    def get_debug_images(self):
        """Get all debug images"""
        return self.debug_images

    def decode_base64_image(self, base64_string):
        """Decode a base64 string to an image"""
        # Remove data:image/jpeg;base64, or data:image/png;base64, if present
        if ',' in base64_string:
            base64_string = base64_string.split(',')[1]

        # Decode base64 string
        image_data = base64.b64decode(base64_string)

        # Convert to numpy array
        nparr = np.frombuffer(image_data, np.uint8)

        # Decode image
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        return image

    def solve_slider_captcha(self, master_image_base64, tile_image_base64=None):
        """
        Solve a slider captcha by finding the position where the tile should be placed

        Args:
            master_image_base64: Base64 encoded string of the master image
            tile_image_base64: Base64 encoded string of the tile image (optional)

        Returns:
            x_position: The x-coordinate where the tile should be placed
        """
        # Decode base64 master image
        master_image = self.decode_base64_image(master_image_base64)

        # Save original image for debugging
        self._save_debug_image("master_original", master_image.copy())

        # Convert master image to grayscale
        master_gray = cv2.cvtColor(master_image, cv2.COLOR_BGR2GRAY)
        self._save_debug_image("master_gray", master_gray)

        # If tile image is provided, use it for template matching
        if tile_image_base64:
            tile_image = self.decode_base64_image(tile_image_base64)
            self._save_debug_image("tile_original", tile_image.copy())

            # Convert tile image to grayscale
            tile_gray = cv2.cvtColor(tile_image, cv2.COLOR_BGR2GRAY)
            self._save_debug_image("tile_gray", tile_gray)

            # Method 2: Template matching
            x_position_template = self._solve_by_template_matching(master_gray, tile_gray)
            logger.info(f"Template matching position: {x_position_template}")

            if x_position_template:
                return x_position_template

        # If no tile image is provided or template matching failed, use other methods

        # Method 1: Edge detection
        x_position_edge = self._solve_by_edge_detection(master_image, master_gray)
        logger.info(f"Edge detection position: {x_position_edge}")

        # Method 3: Pixel intensity analysis
        x_position_intensity = self._solve_by_pixel_intensity(master_gray)
        logger.info(f"Pixel intensity position: {x_position_intensity}")

        # Method 4: Try to detect the puzzle piece automatically
        x_position_auto = self._solve_by_auto_detection(master_image)
        logger.info(f"Auto detection position: {x_position_auto}")

        # Combine results, prioritizing the most reliable methods
        if x_position_auto:
            return x_position_auto
        elif x_position_edge:
            return x_position_edge
        elif x_position_intensity:
            return x_position_intensity
        else:
            # If all methods fail, return a position in the middle right area
            # This is a fallback that might work in some cases
            height, width = master_gray.shape
            return int(width * 0.75)

    def _solve_by_edge_detection(self, master_image, master_gray):
        """Find the position using edge detection"""
        # Apply Canny edge detection
        edges = cv2.Canny(master_gray, 100, 200)
        self._save_debug_image("edges", edges)

        # Find contours
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Draw contours for debugging
        contour_image = master_image.copy()
        cv2.drawContours(contour_image, contours, -1, (0, 255, 0), 2)
        self._save_debug_image("contours", contour_image)

        # Find the contour that likely represents the puzzle hole
        # We're looking for contours with specific characteristics
        potential_holes = []

        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = float(w) / h
            area = cv2.contourArea(contour)

            # Filter based on aspect ratio and area
            # Puzzle holes are typically taller than wide and have a reasonable area
            if 0.5 < aspect_ratio < 2.0 and 100 < area < 5000:
                potential_holes.append((x, y, w, h, area))

        # Sort by area (largest first)
        potential_holes.sort(key=lambda x: x[4], reverse=True)

        if potential_holes:
            # Get the largest potential hole
            x, y, w, h, _ = potential_holes[0]

            # Draw rectangle around the detected hole
            hole_image = master_image.copy()
            cv2.rectangle(hole_image, (x, y), (x + w, y + h), (0, 0, 255), 2)
            self._save_debug_image("detected_hole", hole_image)

            # Return the x-coordinate of the center of the hole
            return x + w // 2

        # Fallback if no suitable contour is found
        return None

    def _solve_by_template_matching(self, master_gray, tile_gray):
        """Find the position using template matching"""
        # Get dimensions
        h, w = tile_gray.shape

        # Apply template matching
        result = cv2.matchTemplate(master_gray, tile_gray, cv2.TM_CCOEFF_NORMED)

        # Find the location of the best match
        _, _, _, max_loc = cv2.minMaxLoc(result)

        # Save the result visualization
        result_vis = cv2.normalize(result, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
        self._save_debug_image("template_matching_result", result_vis)

        # Draw rectangle around the matched region
        match_image = cv2.cvtColor(master_gray.copy(), cv2.COLOR_GRAY2BGR)
        top_left = max_loc
        bottom_right = (top_left[0] + w, top_left[1] + h)
        cv2.rectangle(match_image, top_left, bottom_right, (0, 255, 0), 2)
        self._save_debug_image("template_match", match_image)

        # Return the x-coordinate of the match
        return max_loc[0]

    def _solve_by_pixel_intensity(self, master_gray):
        """Find the position by analyzing pixel intensity along a horizontal line"""
        # Get the middle row of the image
        height = master_gray.shape[0]
        middle_row = master_gray[height // 2, :]

        # Calculate the gradient (difference between adjacent pixels)
        gradient = np.abs(np.diff(middle_row.astype(np.int16)))

        # Find peaks in the gradient (significant changes in intensity)
        # These could indicate the edges of the puzzle hole
        threshold = np.mean(gradient) + 2 * np.std(gradient)
        peaks = np.where(gradient > threshold)[0]

        # Visualize the middle row and gradient
        row_vis = np.zeros((100, len(middle_row)), dtype=np.uint8)
        for i, val in enumerate(middle_row):
            cv2.line(row_vis, (i, 99), (i, 99 - min(val // 3, 99)), 255, 1)

        gradient_vis = np.zeros((100, len(gradient)), dtype=np.uint8)
        for i, val in enumerate(gradient):
            cv2.line(gradient_vis, (i, 99), (i, 99 - min(val * 5, 99)), 255, 1)

        self._save_debug_image("middle_row", row_vis)
        self._save_debug_image("gradient", gradient_vis)

        # Group adjacent peaks to find the edges of the hole
        if len(peaks) > 1:
            groups = []
            current_group = [peaks[0]]

            for i in range(1, len(peaks)):
                if peaks[i] - peaks[i-1] <= 5:  # If peaks are close, they belong to the same edge
                    current_group.append(peaks[i])
                else:
                    groups.append(current_group)
                    current_group = [peaks[i]]

            groups.append(current_group)

            # Filter groups by size (we want significant edges)
            significant_groups = [g for g in groups if len(g) >= 2]

            if len(significant_groups) >= 2:
                # Calculate the center between the first two significant groups
                left_edge = np.mean(significant_groups[0])
                right_edge = np.mean(significant_groups[1])
                return int((left_edge + right_edge) / 2)

        # Fallback if the above method doesn't work
        return None

    def _solve_by_auto_detection(self, master_image):
        """
        Automatically detect the puzzle piece and its target position
        This method uses multiple image processing techniques to find the puzzle piece
        """
        try:
            # Make a copy of the image for processing
            img = master_image.copy()
            height, width = img.shape[:2]

            # Convert to grayscale
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

            # Apply Gaussian blur to reduce noise
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            self._save_debug_image("blurred", blurred)

            # Apply adaptive thresholding
            thresh = cv2.adaptiveThreshold(
                blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY_INV, 11, 2
            )
            self._save_debug_image("threshold", thresh)

            # Find contours
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            # Draw all contours for debugging
            contour_img = img.copy()
            cv2.drawContours(contour_img, contours, -1, (0, 255, 0), 2)
            self._save_debug_image("all_contours", contour_img)

            # Filter contours by size and shape
            puzzle_candidates = []
            for contour in contours:
                # Get bounding rectangle
                x, y, w, h = cv2.boundingRect(contour)

                # Calculate aspect ratio and area
                aspect_ratio = float(w) / h
                area = cv2.contourArea(contour)

                # Filter based on aspect ratio, area, and position
                # Puzzle pieces are typically taller than wide and have a reasonable area
                if 0.5 < aspect_ratio < 2.0 and 100 < area < 5000:
                    # Puzzle pieces are often in the middle-left part of the image
                    if x < width // 2 and y > height // 4 and y < 3 * height // 4:
                        puzzle_candidates.append((x, y, w, h, area, contour))

            # If we found candidates, sort by area (largest first)
            if puzzle_candidates:
                puzzle_candidates.sort(key=lambda c: c[4], reverse=True)

                # Draw the top candidates
                candidate_img = img.copy()
                for i, (x, y, w, h, _, contour) in enumerate(puzzle_candidates[:3]):
                    color = (0, 255, 0) if i == 0 else (0, 0, 255)
                    cv2.rectangle(candidate_img, (x, y), (x + w, y + h), color, 2)
                    cv2.putText(candidate_img, f"{i+1}", (x, y-10), cv2.FONT_HERSHEY_SIMPLEX, 0.9, color, 2)

                self._save_debug_image("puzzle_candidates", candidate_img)

                # Get the most likely puzzle piece
                x, y, w, h, _, _ = puzzle_candidates[0]

                # Now we need to find where this piece should go
                # One approach is to look for a similar shaped gap or a region with different texture

                # Method 1: Look for a region with similar shape but different texture
                # We'll scan the right half of the image

                # Create a mask of the right half of the image
                mask = np.zeros_like(gray)
                mask[:, width//2:] = 255

                # Apply the mask to the thresholded image
                masked_thresh = cv2.bitwise_and(thresh, mask)
                self._save_debug_image("masked_thresh", masked_thresh)

                # Find contours in the right half
                right_contours, _ = cv2.findContours(masked_thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

                # Draw right half contours
                right_contour_img = img.copy()
                cv2.drawContours(right_contour_img, right_contours, -1, (0, 255, 0), 2)
                self._save_debug_image("right_contours", right_contour_img)

                # Look for contours with similar shape to our puzzle piece
                target_candidates = []
                for contour in right_contours:
                    tx, ty, tw, th = cv2.boundingRect(contour)
                    tarea = cv2.contourArea(contour)

                    # Compare with puzzle piece dimensions
                    width_ratio = float(tw) / w
                    height_ratio = float(th) / h
                    area_ratio = float(tarea) / puzzle_candidates[0][4]

                    # If dimensions are similar
                    if (0.8 < width_ratio < 1.2 and
                        0.8 < height_ratio < 1.2 and
                        0.8 < area_ratio < 1.2):
                        target_candidates.append((tx, ty, tw, th, tarea))

                # If we found target candidates
                if target_candidates:
                    # Sort by x-coordinate (leftmost first, as that's likely the target)
                    target_candidates.sort(key=lambda c: c[0])

                    # Draw the target candidates
                    target_img = img.copy()
                    for i, (tx, ty, tw, th, _) in enumerate(target_candidates[:3]):
                        color = (255, 0, 0) if i == 0 else (0, 0, 255)
                        cv2.rectangle(target_img, (tx, ty), (tx + tw, ty + th), color, 2)
                        cv2.putText(target_img, f"T{i+1}", (tx, ty-10), cv2.FONT_HERSHEY_SIMPLEX, 0.9, color, 2)

                    self._save_debug_image("target_candidates", target_img)

                    # Return the x-coordinate of the first target candidate
                    tx, _, tw, _, _ = target_candidates[0]
                    return tx + tw // 2

                # Method 2: If we couldn't find a matching contour, use a heuristic
                # Often the target is in the right half of the image
                # We'll return a position that's proportional to the puzzle piece's position

                # Calculate the relative position of the puzzle piece in the left half
                relative_pos = (x + w/2) / (width/2)

                # Map this to the right half
                target_x = width/2 + relative_pos * (width/2)

                return int(target_x)

            # If we couldn't find a puzzle piece, return None
            return None

        except Exception as e:
            logger.error(f"Error in auto detection: {e}")
            return None

# Example usage
if __name__ == "__main__":
    # This is just an example, replace with your actual base64 images
    master_image_base64 = "your_master_image_base64_here"
    tile_image_base64 = "your_tile_image_base64_here"

    solver = CaptchaSolver()
    solver.enable_debug(True)

    x_position = solver.solve_slider_captcha(master_image_base64, tile_image_base64)
    print(f"The tile should be placed at x-coordinate: {x_position}")

    # You can access debug images if needed
    debug_images = solver.get_debug_images()
