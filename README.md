# Automatisation SeLogerPro

Ce projet permet d'automatiser le renouvellement des annonces immobilières sur la plateforme SeLogerPro, en gérant efficacement les captchas et les mécanismes anti-bot.

## Fonctionnalités

- **Connexion automatique** au compte SeLogerPro avec simulation de comportement humain
- **Résolution de captchas** via 2Captcha ou méthodes internes
- **Détection des annonces expirées** ou proches de l'expiration
- **Renouvellement automatique** des annonces selon différentes stratégies
- **Notifications par webhook** pour suivre l'état des opérations
- **Journalisation détaillée** et téléversement vers AWS S3
- **Gestion des erreurs** avec politique de réessai
- **Conformité RGPD** avec anonymisation des données sensibles
- **Captures d'écran horodatées** pour faciliter le débogage
- **Proxy rotatif résidentiel** avec IProyal pour éviter la détection

## Prérequis

- Python 3.8+
- Playwright
- Autres dépendances listées dans `requirements.txt`

## Installation

### Installation automatique (recommandée)

1. Clonez ce dépôt
2. Exécutez le script d'installation :

```bash
python install_dependencies.py
```

Ce script va :
- Vérifier la version de Python
- Installer toutes les dépendances
- Installer les navigateurs Playwright
- Créer les répertoires nécessaires
- Vérifier la configuration

### Installation manuelle

1. Clonez ce dépôt
2. Installez les dépendances :

```bash
pip install -r requirements.txt
playwright install
```

3. Créez les répertoires nécessaires :

```bash
mkdir screenshots auth data
```

4. Configurez le fichier `config.json` avec vos informations

## Configuration

Le fichier `config.json` contient toutes les configurations nécessaires :

```json
{
  "auth": {
    "username": "VOTRE_IDENTIFIANT",
    "password": "VOTRE_MOT_DE_PASSE",
    "2fa_enabled": false,
    "encryption": {
      "algorithm": "AES-256-CBC",
      "key_derivation_iterations": 100000
    },
    "captcha": {
      "service": "2captcha",
      "api_key": "VOTRE_CLE_API_2CAPTCHA"
    }
  },
  "renewal": {
    "strategy": "create_new_from_template",
    "schedule": {
      "frequency": "daily",
      "execution_window": {
        "start": "12:45",
        "end": "13:15"
      },
      "random_delay": {
        "min_seconds": 60,
        "max_seconds": 300
      }
    },
    "listing_age_threshold_days": 4,
    "max_properties_per_run": 0
  },
  "notifications": {
    "webhook": {
      "url": "https://votre-webapp.com/api/renewal-events",
      "headers": {
        "Content-Type": "application/json",
        "X-API-Key": "VOTRE_API_KEY"
      },
      "on_error_only": true
    }
  },
  "error_handling": {
    "retry_policy": {
      "max_retries": 3,
      "backoff_factor": 2.5
    },
    "error_logs": {
      "detailed_tracing": true,
      "screenshot_on_error": true
    }
  },
  "logging": {
    "level": "DEBUG"
  },
  "browser": {
    "type": "chromium",
    "launch_timeout": 30,
    "navigation_timeout": 30
  },
  "proxy": {
    "enabled": true,
    "hostname": "geo.iproyal.com",
    "port": 12321,
    "username": "VOTRE_USERNAME_IPROYAL",
    "password": "VOTRE_PASSWORD_IPROYAL",
    "countries": ["fr", "de", "es", "it", "nl", "be"],
    "rotation_interval_minutes": 10,
    "max_failures_per_proxy": 3,
    "timeout_seconds": 30
  }
}
```

## Utilisation

### Exécution standard

```bash
python seloger_renewer.py --config config.json
```

### Options disponibles

```bash
python seloger_renewer.py --help
```

Voici les options disponibles :

- `--config`, `-c` : Chemin vers le fichier de configuration (défaut: `config.json`)
- `--simulation`, `-s` : Exécuter en mode simulation (sans connexion réelle)
- `--verbose`, `-v` : Afficher les messages de débogage détaillés
- `--visible`, `-d` : Afficher le navigateur (mode non-headless)
- `--url`, `-u` : URL de départ personnalisée (défaut: `https://myselogerpro.com/login`)
- `--username` : Nom d'utilisateur (remplace celui du fichier config)
- `--password` : Mot de passe (remplace celui du fichier config)
- `--continue-after-captcha-failure` : Continuer l'exécution même après un échec de captcha

### Exemples de commandes

1. Exécution en mode visible (navigateur affiché) :
```bash
python seloger_renewer.py --visible
```

2. Exécution avec identifiants spécifiques :
```bash
python seloger_renewer.py --username "votre_identifiant" --password "votre_mot_de_passe"
```

3. Exécution en mode simulation (pour tester sans effectuer de connexion réelle) :
```bash
python seloger_renewer.py --simulation
```

4. Exécution avec URL personnalisée :
```bash
python seloger_renewer.py --url "https://myselogerpro.com/annonce/portefeuille"
```

5. Exécution avec configuration personnalisée :
```bash
python seloger_renewer.py --config ma_config.json
```

## Tests

### Test de la configuration des proxies

Pour tester votre configuration de proxies :

```bash
# Test complet de la configuration
python test_proxy.py

# Test de connectivité uniquement
python test_proxy.py --connectivity
```

### Test de l'installation

Pour vérifier que toutes les dépendances sont correctement installées :

```bash
python install_dependencies.py
```

## Structure du projet

- `seloger_renewer.py` : Script principal avec gestion des proxies
- `captcha_solver.py` : Module de résolution des captchas
- `config.json` : Fichier de configuration
- `requirements.txt` : Liste des dépendances
- `install_dependencies.py` : Script d'installation automatique
- `test_proxy.py` : Script de test des proxies
- `screenshots/` : Dossier contenant les captures d'écran horodatées
- `auth/` : Dossier contenant les informations d'authentification sauvegardées
- `data/` : Dossier contenant les données temporaires

## Fonctionnement

1. Le script se connecte à SeLogerPro en simulant un comportement humain
2. Il gère les captchas et les mécanismes anti-bot
3. Il navigue vers la page des annonces
4. Il identifie les annonces expirées ou proches de l'expiration
5. Il renouvelle les annonces selon la stratégie configurée
6. Il envoie des notifications via webhook si configuré
7. Il sauvegarde l'état d'authentification pour les futures exécutions

## Configuration des proxies

Le système supporte les proxies rotatifs résidentiels IProyal pour améliorer l'anonymat et éviter la détection :

### Configuration IProyal

1. Créez un compte sur [IProyal](https://iproyal.com)
2. Obtenez vos informations de connexion (hostname, port, username, password)
3. Configurez la section `proxy` dans votre `config.json` :

```json
{
  "proxy": {
    "enabled": true,
    "hostname": "geo.iproyal.com",
    "port": 12321,
    "username": "VOTRE_USERNAME_IPROYAL",
    "password": "VOTRE_PASSWORD_IPROYAL",
    "countries": ["fr", "de", "es", "it", "nl", "be"],
    "rotation_interval_minutes": 10,
    "max_failures_per_proxy": 3,
    "timeout_seconds": 30
  }
}
```

### Paramètres de configuration

- `enabled` : Active/désactive l'utilisation des proxies
- `hostname` : Nom d'hôte du service proxy IProyal
- `port` : Port de connexion
- `username` : Nom d'utilisateur IProyal
- `password` : Mot de passe IProyal (peut inclure des paramètres de pays)
- `countries` : Liste des pays pour la rotation géographique
- `rotation_interval_minutes` : Intervalle de rotation automatique en minutes
- `max_failures_per_proxy` : Nombre maximum d'échecs avant rotation forcée
- `timeout_seconds` : Timeout pour les tests de connectivité

### Fonctionnalités

- **Rotation automatique** : Change de proxy selon l'intervalle configuré
- **Rotation géographique** : Utilise différents pays pour varier l'origine
- **Gestion des échecs** : Rotation automatique en cas de problème
- **Test de connectivité** : Vérifie la fonctionnalité avant utilisation
- **Fallback** : Utilise une connexion directe si aucun proxy ne fonctionne

## Stratégies de renouvellement

- `create_new_from_template` : Crée une nouvelle annonce à partir du modèle de l'annonce expirée
- D'autres stratégies peuvent être implémentées selon les besoins

## Résolution des problèmes courants

### Problème de captcha

Si le captcha n'est pas résolu correctement :
1. Vérifiez votre clé API 2Captcha
2. Utilisez l'option `--visible` pour voir ce qui se passe
3. Vérifiez les captures d'écran dans le dossier `screenshots/`

### Détection comme bot

Si le site vous détecte comme un bot :
1. Augmentez les délais aléatoires dans la configuration
2. Utilisez l'option `--visible` pour exécuter en mode visible
3. Vérifiez les captures d'écran pour identifier à quel moment la détection se produit

### Erreurs de connexion

Si vous rencontrez des erreurs de connexion :
1. Vérifiez vos identifiants
2. Vérifiez votre connexion internet
3. Vérifiez si le site est accessible manuellement

### Problèmes de proxy

Si vous rencontrez des problèmes avec les proxies :
1. Vérifiez vos informations de connexion IProyal
2. Testez la connectivité manuellement
3. Vérifiez les logs pour identifier les erreurs spécifiques
4. Désactivez temporairement les proxies (`"enabled": false`) pour tester
5. Ajustez les paramètres de timeout et de rotation selon vos besoins

## Sécurité

Les informations d'authentification sont chiffrées avec AES-256-CBC. Assurez-vous de protéger votre fichier de configuration.

## Intégration avec une application web

Le script peut être intégré à une application web via des webhooks pour signaler les résultats des opérations de renouvellement.

## Licence

Ce projet est sous licence MIT.
