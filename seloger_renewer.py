#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
import yaml
import time
import random
import logging
import asyncio
import base64
import hashlib
import datetime
import io
import requests
import boto3
import tempfile
import cv2
import numpy as np
import math
import aiohttp
from typing import Dict, List, Optional, Union, Any, Tuple
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding
from PIL import Image, ImageDraw, ImageFont

# Import our captcha solver
from captcha_solver import CaptchaSolver
from cryptography.hazmat.backends import default_backend
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON>, BrowserContext
from twocaptcha import TwoCaptcha
from fake_useragent import UserAgent

# Import stealth modules
try:
    import playwright_stealth
    STEALTH_AVAILABLE = True
except ImportError:
    STEALTH_AVAILABLE = False

try:
    import undetected_playwright
    UNDETECTED_AVAILABLE = True
except ImportError:
    UNDETECTED_AVAILABLE = False
try:
    import cv2
    import numpy as np
    from PIL import Image
    import pytesseract
    CV_AVAILABLE = True
except ImportError:
    CV_AVAILABLE = False

try:
    from opentelemetry import trace
    from opentelemetry.sdk.trace import TracerProvider
    from opentelemetry.sdk.trace.export import BatchSpanProcessor
    from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
    TELEMETRY_AVAILABLE = True
except ImportError:
    TELEMETRY_AVAILABLE = False

# Configuration du traçage OpenTelemetry
if TELEMETRY_AVAILABLE:
    trace.set_tracer_provider(TracerProvider())
    tracer = trace.get_tracer(__name__)
else:
    # Créer un tracer factice si OpenTelemetry n'est pas disponible
    class DummySpan:
        def __enter__(self): return self
        def __exit__(self, exc_type, exc_val, exc_tb): pass
        def add_event(self, *args, **kwargs): pass
        def set_attribute(self, *args, **kwargs): pass
        def record_exception(self, *args, **kwargs): pass

    class DummyTracer:
        def start_as_current_span(self, name, *args, **kwargs):
            return DummySpan()

    tracer = DummyTracer()

class CircuitBreaker:
    """Implémentation du pattern Circuit Breaker pour gérer les erreurs réseau"""

    def __init__(self, max_failures=3, reset_timeout=60):
        self.max_failures = max_failures
        self.reset_timeout = reset_timeout
        self.failures = 0
        self.state = "CLOSED"
        self.last_failure_time = 0

    async def execute(self, func, *args, **kwargs):
        if self.state == "OPEN":
            if time.time() - self.last_failure_time > self.reset_timeout:
                self.state = "HALF-OPEN"
            else:
                raise Exception("Circuit breaker is OPEN")

        try:
            result = await func(*args, **kwargs)
            if self.state == "HALF-OPEN":
                self.state = "CLOSED"
                self.failures = 0
            return result
        except Exception as e:
            self.failures += 1
            self.last_failure_time = time.time()
            if self.failures >= self.max_failures:
                self.state = "OPEN"
            raise e


class ProxyRotator:
    """Gestionnaire de proxies rotatifs résidentiels IProyal"""

    def __init__(self, config: Dict[str, Any], logger: logging.Logger):
        self.config = config
        self.logger = logger
        self.current_proxy = None
        self.proxy_failures = {}
        self.last_rotation_time = 0
        self.rotation_interval = config.get("rotation_interval_minutes", 10) * 60  # Convertir en secondes
        self.max_failures_per_proxy = config.get("max_failures_per_proxy", 3)
        self.proxy_timeout = config.get("timeout_seconds", 30)

        # Configuration IProyal
        self.hostname = config.get("hostname", "geo.iproyal.com")
        self.port = config.get("port", 12321)
        self.username = config.get("username", "")
        self.password = config.get("password", "")

        # Liste des pays disponibles pour la rotation géographique
        self.countries = config.get("countries", ["fr", "de", "es", "it", "nl", "be"])
        self.current_country_index = 0

        # Options pour forcer une nouvelle session IP
        self.force_new_session = config.get("force_new_session", True)
        self.session_rotation_per_run = config.get("session_rotation_per_run", True)
        self.session_id = None

        self.logger.info(f"ProxyRotator initialisé avec {len(self.countries)} pays disponibles")
        self.logger.info(f"Force nouvelle session: {self.force_new_session}")
        self.logger.info(f"Rotation de session par exécution: {self.session_rotation_per_run}")

    def _generate_proxy_config(self, country: str = None) -> Dict[str, Any]:
        """Génère une configuration de proxy pour un pays donné"""
        if country is None:
            country = self.countries[self.current_country_index]
            self.current_country_index = (self.current_country_index + 1) % len(self.countries)

        # Format correct selon le curl qui fonctionne:
        # Username reste inchangé, le pays est ajouté au mot de passe
        username = self.username
        password_with_country = f"{self.password}_country-{country}"

        # Générer un nouvel ID de session si nécessaire
        if self.force_new_session and (self.session_id is None or self.session_rotation_per_run):
            import uuid
            self.session_id = str(uuid.uuid4())[:8]  # ID de session court
            self.logger.info(f"Nouvelle session générée: {self.session_id}")

        # Ajouter l'ID de session au mot de passe si configuré
        # Note: IProyal ne supporte peut-être pas les sessions personnalisées
        # Désactivé temporairement pour les tests
        # if self.session_id and self.force_new_session:
        #     password_with_country = f"{password_with_country}_session-{self.session_id}"

        proxy_config = {
            "server": f"http://{self.hostname}:{self.port}",
            "username": username,
            "password": password_with_country,
            "country": country,
            "session_id": self.session_id,
            "full_proxy_url": f"http://{username}:{password_with_country}@{self.hostname}:{self.port}"
        }

        self.logger.debug(f"Configuration proxy générée pour le pays: {country}")
        self.logger.debug(f"Password avec pays et session: {password_with_country}")
        return proxy_config

    async def get_current_proxy(self) -> Optional[Dict[str, Any]]:
        """Retourne le proxy actuel ou en génère un nouveau si nécessaire"""
        current_time = time.time()

        # Vérifier si une rotation est nécessaire
        should_rotate = (
            self.current_proxy is None or
            current_time - self.last_rotation_time > self.rotation_interval or
            self._proxy_has_too_many_failures(self.current_proxy)
        )

        if should_rotate:
            await self._rotate_proxy()

        return self.current_proxy

    async def _rotate_proxy(self):
        """Effectue une rotation du proxy"""
        old_proxy = self.current_proxy

        # Essayer plusieurs pays jusqu'à trouver un proxy fonctionnel
        for attempt in range(len(self.countries)):
            try:
                new_proxy = self._generate_proxy_config()

                # Tester le nouveau proxy
                if await self._test_proxy(new_proxy):
                    self.current_proxy = new_proxy
                    self.last_rotation_time = time.time()

                    if old_proxy:
                        self.logger.info(f"Proxy rotaté: {old_proxy['country']} -> {new_proxy['country']}")
                    else:
                        self.logger.info(f"Nouveau proxy activé: {new_proxy['country']}")

                    return
                else:
                    self.logger.warning(f"Échec du test pour le proxy {new_proxy['country']}")

            except Exception as e:
                self.logger.error(f"Erreur lors de la rotation du proxy: {e}")

        # Si aucun proxy ne fonctionne, utiliser une connexion directe
        self.logger.warning("Aucun proxy fonctionnel trouvé, utilisation d'une connexion directe")
        self.current_proxy = None

    async def _test_proxy(self, proxy_config: Dict[str, Any]) -> bool:
        """Teste si un proxy fonctionne correctement"""
        try:
            # Utiliser l'URL complète du proxy depuis la configuration
            proxy_url = proxy_config.get('full_proxy_url')
            if not proxy_url:
                proxy_url = f"http://{proxy_config['username']}:{proxy_config['password']}@{self.hostname}:{self.port}"

            # Augmenter le timeout pour les proxies résidentiels
            timeout = aiohttp.ClientTimeout(total=60, connect=30)

            self.logger.debug(f"Test de connectivité avec: {proxy_url[:50]}...")

            # Utiliser aiohttp avec proxy de manière simple
            async with aiohttp.ClientSession(timeout=timeout) as session:
                # Tester avec un site simple pour vérifier la connectivité
                test_urls = [
                    "http://httpbin.org/ip",
                    "https://ipv4.icanhazip.com"
                ]

                for test_url in test_urls:
                    try:
                        self.logger.debug(f"Test avec {test_url}...")
                        async with session.get(test_url, proxy=proxy_url) as response:
                            if response.status == 200:
                                if "httpbin.org" in test_url:
                                    data = await response.json()
                                    ip = data.get('origin', 'inconnue')
                                else:
                                    ip = (await response.text()).strip()

                                self.logger.info(f"Test proxy réussi, IP: {ip}, Pays: {proxy_config.get('country', 'N/A')}")
                                return True
                            else:
                                self.logger.debug(f"Status HTTP {response.status} pour {test_url}")
                    except Exception as url_error:
                        self.logger.debug(f"Erreur avec {test_url}: {url_error}")
                        continue

                self.logger.debug("Tous les tests d'URL ont échoué")
                return False

        except Exception as e:
            self.logger.debug(f"Erreur lors du test du proxy: {e}")
            return False

    def _proxy_has_too_many_failures(self, proxy_config: Optional[Dict[str, Any]]) -> bool:
        """Vérifie si un proxy a trop d'échecs"""
        if proxy_config is None:
            return False

        proxy_key = f"{proxy_config['country']}"
        failures = self.proxy_failures.get(proxy_key, 0)
        return failures >= self.max_failures_per_proxy

    def report_proxy_failure(self, proxy_config: Optional[Dict[str, Any]]):
        """Signale un échec pour le proxy actuel"""
        if proxy_config is None:
            return

        proxy_key = f"{proxy_config['country']}"
        self.proxy_failures[proxy_key] = self.proxy_failures.get(proxy_key, 0) + 1

        self.logger.warning(f"Échec signalé pour le proxy {proxy_key} "
                          f"({self.proxy_failures[proxy_key]}/{self.max_failures_per_proxy})")

    def reset_proxy_failures(self, proxy_config: Optional[Dict[str, Any]]):
        """Remet à zéro les échecs pour un proxy"""
        if proxy_config is None:
            return

        proxy_key = f"{proxy_config['country']}"
        if proxy_key in self.proxy_failures:
            del self.proxy_failures[proxy_key]
            self.logger.debug(f"Échecs remis à zéro pour le proxy {proxy_key}")

    async def force_rotation(self):
        """Force une rotation immédiate du proxy"""
        self.logger.info("Rotation forcée du proxy")
        # Forcer une nouvelle session si configuré
        if self.force_new_session:
            self.session_id = None
        await self._rotate_proxy()

    def get_playwright_proxy_config(self) -> Optional[Dict[str, str]]:
        """Retourne la configuration proxy pour Playwright"""
        if not self.current_proxy:
            return None

        return {
            "server": f"http://{self.hostname}:{self.port}",
            "username": self.current_proxy["username"],
            "password": self.current_proxy["password"]
        }

    async def test_current_proxy_connectivity(self) -> bool:
        """Teste la connectivité du proxy actuel"""
        if not self.current_proxy:
            return False
        return await self._test_proxy(self.current_proxy)

    def get_current_proxy_info(self) -> Dict[str, Any]:
        """Retourne les informations du proxy actuel"""
        if not self.current_proxy:
            return {"status": "no_proxy", "country": None, "session_id": None}

        return {
            "status": "active",
            "country": self.current_proxy.get("country"),
            "session_id": self.current_proxy.get("session_id"),
            "hostname": self.hostname,
            "port": self.port
        }

class CryptoHandler:
    """Gestionnaire de chiffrement pour les données sensibles"""

    def __init__(self, config):
        self.config = config
        self.iterations = config["auth"]["encryption"]["key_derivation_iterations"]
        self.salt = os.urandom(16)

    def derive_key(self, password: str) -> bytes:
        """Dérive une clé à partir du mot de passe"""
        return hashlib.pbkdf2_hmac(
            'sha256',
            password.encode(),
            self.salt,
            self.iterations,
            32
        )

    def encrypt(self, data: str, password: str) -> str:
        """Chiffre les données avec AES-256-CBC"""
        key = self.derive_key(password)
        iv = os.urandom(16)
        padder = padding.PKCS7(128).padder()
        padded_data = padder.update(data.encode()) + padder.finalize()

        cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
        encryptor = cipher.encryptor()
        encrypted_data = encryptor.update(padded_data) + encryptor.finalize()

        # Combine salt, iv, et données chiffrées
        result = self.salt + iv + encrypted_data
        return base64.b64encode(result).decode()

    def decrypt(self, encrypted_data: str, password: str) -> str:
        """Déchiffre les données"""
        data = base64.b64decode(encrypted_data)
        salt, iv, ciphertext = data[:16], data[16:32], data[32:]

        key = hashlib.pbkdf2_hmac('sha256', password.encode(), salt, self.iterations, 32)

        cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
        decryptor = cipher.decryptor()
        padded_data = decryptor.update(ciphertext) + decryptor.finalize()

        unpadder = padding.PKCS7(128).unpadder()
        data = unpadder.update(padded_data) + unpadder.finalize()

        return data.decode()

class DOMBertAnalyzer:
    """Analyseur de DOM utilisant BERT pour détecter les éléments"""

    def __init__(self):
        # Initialisation du modèle (simulation)
        self.model_loaded = True

    async def analyze_page(self, page: Page) -> Dict[str, str]:
        """Analyse la page et retourne les sélecteurs des éléments importants"""
        with tracer.start_as_current_span("dom_bert_analysis"):
            # Simulation d'analyse DOM-BERT
            html_content = await page.content()
            # Analyse du contenu (simulation)
            time.sleep(0.5)

            # Retourne des sélecteurs détectés (à adapter selon le site réel)
            return {
                "login_button": "button.login-button",
                "username_field": "input[name='username']",
                "password_field": "input[name='password']",
                "submit_button": "button[type='submit']",
                "property_list": ".property-list-container",
                "expired_property": ".property-card.expired",
                "renew_button": "button.renew-property"
            }

class VisionTransformer:
    """Modèle de vision pour analyser la structure visuelle de la page"""

    def __init__(self):
        # Initialisation du modèle (simulation)
        self.cv_available = CV_AVAILABLE

    async def analyze_screenshot(self, page: Page) -> List[Dict]:
        """Analyse une capture d'écran pour détecter les éléments visuels"""
        with tracer.start_as_current_span("vision_analysis"):
            # Capture d'écran
            screenshot = await page.screenshot(type="jpeg")

            if not self.cv_available:
                # Fallback si OpenCV n'est pas disponible
                return [
                    {"type": "button", "text": "Renouveler", "confidence": 0.95, "bbox": [100, 200, 200, 240]},
                    {"type": "status", "text": "Expiré", "confidence": 0.92, "bbox": [300, 150, 350, 170]}
                ]

            try:
                # Conversion en image pour traitement
                nparr = np.frombuffer(screenshot, np.uint8)
                img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

                # Simulation d'analyse d'image
                # Dans un cas réel, utiliserait un modèle de vision par ordinateur
            except Exception as e:
                logging.warning(f"Erreur lors de l'analyse d'image: {e}")

            # Retourne les éléments détectés (simulation)
            return [
                {"type": "button", "text": "Renouveler", "confidence": 0.95, "bbox": [100, 200, 200, 240]},
                {"type": "status", "text": "Expiré", "confidence": 0.92, "bbox": [300, 150, 350, 170]}
            ]

    async def detect_expired_properties(self, page: Page) -> List[Dict]:
        """Détecte les propriétés expirées par analyse visuelle"""
        with tracer.start_as_current_span("expired_detection"):
            # Capture d'écran
            screenshot = await page.screenshot(type="jpeg")

            if not self.cv_available:
                # Fallback si les bibliothèques de vision ne sont pas disponibles
                return [{"id": "property123", "confidence": 0.9}]

            try:
                # OCR pour détecter le texte "expiré" ou similaire
                img = Image.open(io.BytesIO(screenshot))
                text = pytesseract.image_to_string(img, lang='fra')

                # Analyse du texte pour trouver les mentions d'expiration (simulation)
                if "expiré" in text.lower() or "expire" in text.lower():
                    return [{"id": "property123", "confidence": 0.9}]
            except Exception as e:
                logging.warning(f"Erreur lors de la détection OCR: {e}")

            # Retourne une liste vide si rien n'est trouvé
            return []

class SeLogerRenewer:
    """Classe principale pour le renouvellement des annonces sur SeLogerPro"""

    def __init__(self, config_path: str, simulation_mode: bool = False, continue_after_captcha_failure: bool = False):
        """Initialise le renouveleur avec la configuration"""
        self.load_config(config_path)
        self.dom_analyzer = DOMBertAnalyzer()
        self.vision_model = VisionTransformer()
        self.crypto_handler = CryptoHandler(self.config)
        self.circuit_breaker = CircuitBreaker(
            max_failures=self.config["error_handling"]["retry_policy"]["max_retries"],
            reset_timeout=60
        )

        # Initialiser le gestionnaire de proxy si configuré
        self.proxy_rotator = None
        if self.config.get("proxy", {}).get("enabled", False):
            self.proxy_rotator = ProxyRotator(
                self.config["proxy"],
                self._setup_logger()
            )
        self.simulation_mode = simulation_mode
        self.continue_after_captcha_failure = continue_after_captcha_failure
        self.logger = self._setup_logger()

        # Créer les dossiers nécessaires s'ils n'existent pas
        self.data_dir = "data"
        self.auth_dir = "auth"
        self.screenshots_dir = "screenshots"

        for directory in [self.data_dir, self.auth_dir, self.screenshots_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory)
                self.logger.info(f"Dossier '{directory}' créé")

        if self.simulation_mode:
            self.logger.info("Mode de simulation activé - aucune connexion réelle ne sera effectuée")

        if self.continue_after_captcha_failure:
            self.logger.info("Mode continue_after_captcha_failure activé - l'exécution continuera même après un échec de captcha")

    def load_config(self, config_path: str) -> None:
        """Charge la configuration depuis un fichier YAML ou JSON"""
        with open(config_path, 'r') as f:
            if config_path.endswith('.yaml') or config_path.endswith('.yml'):
                self.config = yaml.safe_load(f)
            else:
                self.config = json.load(f)

    def _setup_logger(self) -> logging.Logger:
        """Configure le système de journalisation"""
        # Utiliser un logger unique pour éviter les doublons
        logger = logging.getLogger("seloger_renewer")

        # Éviter la propagation des logs vers le logger racine
        logger.propagate = False

        # Réinitialiser les handlers existants pour éviter les doublons
        if logger.handlers:
            logger.handlers.clear()

        logger.setLevel(getattr(logging, self.config["logging"]["level"]))

        # Handler pour la console
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        ))
        logger.addHandler(console_handler)

        # Autres handlers selon la configuration

        return logger

    async def screenshot_with_timestamp(self, page: Page, path: str, clip=None) -> None:
        """Prend une capture d'écran et ajoute un horodatage"""
        try:
            # Prendre la capture d'écran
            screenshot_bytes = await page.screenshot(clip=clip)

            # Convertir en image PIL
            img = Image.open(io.BytesIO(screenshot_bytes))

            # Préparer le texte de l'horodatage
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Créer un objet de dessin
            draw = ImageDraw.Draw(img)

            # Essayer de charger une police, sinon utiliser la police par défaut
            try:
                # Essayer de trouver une police système
                font_size = 20
                font_paths = [
                    "C:\\Windows\\Fonts\\Arial.ttf",  # Windows
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # Linux
                    "/System/Library/Fonts/Helvetica.ttc"  # macOS
                ]

                font = None
                for font_path in font_paths:
                    if os.path.exists(font_path):
                        font = ImageFont.truetype(font_path, font_size)
                        break
            except Exception:
                # Si aucune police n'est trouvée, utiliser la police par défaut
                font = None

            # Obtenir les dimensions de l'image
            width, height = img.size

            # Dessiner un rectangle semi-transparent pour le fond du texte
            text_bg_color = (0, 0, 0, 128)  # Noir semi-transparent
            text_padding = 5
            text_width = width
            text_height = font_size + 2 * text_padding if font else 30

            # Créer une image pour le fond semi-transparent
            text_bg = Image.new('RGBA', (text_width, text_height), text_bg_color)

            # Superposer le fond sur l'image principale
            img.paste(text_bg, (0, height - text_height), text_bg)

            # Ajouter le texte
            text_color = (255, 255, 255)  # Blanc
            text_position = (text_padding, height - text_height + text_padding)

            draw.text(text_position, timestamp, fill=text_color, font=font)

            # Sauvegarder l'image
            os.makedirs(os.path.dirname(path), exist_ok=True)
            img.save(path)

            self.logger.debug(f"Capture d'écran avec horodatage sauvegardée: {path}")
        except Exception as e:
            self.logger.error(f"Erreur lors de la capture d'écran avec horodatage: {e}")
            # Fallback: sauvegarder la capture d'écran sans horodatage
            try:
                await page.screenshot(path=path, clip=clip)
                self.logger.debug(f"Capture d'écran sans horodatage sauvegardée: {path}")
            except Exception as e2:
                self.logger.error(f"Erreur lors de la capture d'écran de secours: {e2}")

    async def save_authentication_state(self, context: BrowserContext, password: str) -> None:
        """Sauvegarde l'état d'authentification de manière sécurisée"""
        with tracer.start_as_current_span("save_auth_state"):
            try:
                self.logger.info("Sauvegarde de l'état d'authentification...")

                # Récupération de l'état de stockage (cookies, localStorage, etc.)
                storage_state = await context.storage_state()

                # Ajout d'informations supplémentaires pour le débogage
                storage_state["saved_at"] = datetime.datetime.now().isoformat()
                storage_state["username"] = self.config["auth"]["username"]

                # Conversion en JSON
                storage_json = json.dumps(storage_state, indent=2)

                # Sauvegarde d'une copie non chiffrée pour le débogage si nécessaire
                auth_dir = "auth"
                if not os.path.exists(auth_dir):
                    os.makedirs(auth_dir)

                with open(f"{auth_dir}/auth_state_debug.json", "w") as f:
                    json.dump(storage_state, f, indent=2)

                # Chiffrement des données d'authentification
                encrypted_state = self.crypto_handler.encrypt(storage_json, password)

                # Sauvegarde de l'état chiffré
                with open("auth_state.enc", "w") as f:
                    f.write(encrypted_state)

                self.logger.info("État d'authentification sauvegardé avec succès")

                # Afficher le nombre de cookies sauvegardés
                if "cookies" in storage_state:
                    num_cookies = len(storage_state["cookies"])
                    self.logger.info(f"{num_cookies} cookies sauvegardés")

                    # Afficher les domaines des cookies pour le débogage
                    domains = set(cookie.get("domain", "") for cookie in storage_state["cookies"])
                    self.logger.info(f"Domaines des cookies: {', '.join(domains)}")

                    # Vérifier si les cookies importants sont présents
                    important_domains = [
                        "myselogerpro.com",
                        "selogerpro.com",
                        "seloger.com"
                    ]

                    missing_domains = [domain for domain in important_domains if not any(domain in cookie_domain for cookie_domain in domains)]

                    if missing_domains:
                        self.logger.warning(f"Attention: Cookies manquants pour les domaines: {', '.join(missing_domains)}")
                        self.logger.info("Les cookies manquants peuvent causer des problèmes d'authentification lors des prochaines exécutions")
                    else:
                        self.logger.info("Tous les cookies importants ont été sauvegardés")

            except Exception as e:
                self.logger.error(f"Erreur lors de la sauvegarde de l'état d'authentification: {e}")

    async def load_authentication_state(self, context: BrowserContext, password: str) -> bool:
        """Charge l'état d'authentification s'il existe"""
        auth_file = "auth_state.enc"
        auth_state_json = os.path.join(self.data_dir, "auth_state.json")
        local_storage_json = os.path.join(self.data_dir, "local_storage.json")

        # Vérifier d'abord le fichier auth_state.json (non chiffré)
        if os.path.exists(auth_state_json):
            try:
                self.logger.info(f"Chargement de l'état d'authentification depuis {auth_state_json}")
                with open(auth_state_json, "r") as f:
                    auth_data = json.load(f)

                if "cookies" in auth_data:
                    # Vérifier si les cookies sont récents (moins de 24 heures)
                    is_recent = True
                    if len(auth_data["cookies"]) > 0:
                        # Vérifier l'expiration des cookies
                        now = time.time()
                        expired_cookies = 0
                        for cookie in auth_data["cookies"]:
                            if "expires" in cookie and cookie["expires"] < now:
                                expired_cookies += 1

                        if expired_cookies > len(auth_data["cookies"]) / 2:
                            self.logger.warning(f"{expired_cookies} cookies sur {len(auth_data['cookies'])} sont expirés")
                            is_recent = False

                    if is_recent:
                        # Appliquer les cookies
                        await context.add_cookies(auth_data["cookies"])
                        self.logger.info(f"Chargé {len(auth_data['cookies'])} cookies depuis {auth_state_json}")

                        # Charger également le stockage local si disponible
                        if os.path.exists(local_storage_json):
                            try:
                                with open(local_storage_json, "r") as f:
                                    local_storage = json.load(f)

                                # Appliquer le stockage local
                                page = await context.new_page()
                                for key, value in local_storage.items():
                                    # Échapper les apostrophes dans la valeur
                                    escaped_value = value.replace("'", "\\'")
                                    await page.evaluate(f"""
                                        localStorage.setItem('{key}', '{escaped_value}');
                                    """)
                                await page.close()
                                self.logger.info(f"Stockage local chargé depuis {local_storage_json}")
                            except Exception as ls_error:
                                self.logger.warning(f"Erreur lors du chargement du stockage local: {ls_error}")

                        return True
                    else:
                        self.logger.warning("Les cookies sont expirés, nouvelle authentification requise")
            except Exception as e:
                self.logger.warning(f"Erreur lors du chargement de l'état d'authentification depuis {auth_state_json}: {e}")

        # Si le fichier non chiffré n'existe pas ou a échoué, essayer le fichier chiffré
        if not os.path.exists(auth_file):
            self.logger.info("Aucun état d'authentification trouvé. Première exécution détectée.")
            return False

        try:
            with open(auth_file, "r") as f:
                encrypted_state = f.read()

            # Déchiffrement
            storage_json = self.crypto_handler.decrypt(encrypted_state, password)
            storage_state = json.loads(storage_json)

            # Application de l'état
            # Utiliser la méthode correcte pour appliquer l'état de stockage
            await context.add_cookies(storage_state.get("cookies", []))

            # Appliquer localStorage si disponible
            if "origins" in storage_state:
                for origin in storage_state["origins"]:
                    if "localStorage" in origin:
                        for item in origin["localStorage"]:
                            await context.add_init_script("""
                                window.localStorage.setItem('{}', '{}');
                            """.format(item["name"], item["value"].replace("'", "\\'")))

            self.logger.info("État d'authentification chargé avec succès depuis le fichier chiffré")
            return True
        except FileNotFoundError:
            self.logger.info("Aucun état d'authentification trouvé. Première exécution détectée.")
            return False
        except json.JSONDecodeError:
            self.logger.warning("État d'authentification corrompu. Nouvelle authentification requise.")
            # Suppression du fichier corrompu
            if os.path.exists(auth_file):
                os.remove(auth_file)
            return False
        except Exception as e:
            self.logger.warning(f"Impossible de charger l'état d'authentification: {e}")
            return False

    async def check_for_blocking(self, page: Page) -> bool:
        """Vérifie si l'utilisateur est bloqué par le site"""
        try:
            # Prendre une capture d'écran pour analyse future
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            screenshot_path = f"screenshots/check_blocking_{timestamp}.png"
            await page.screenshot(path=screenshot_path)

            # Vérification spécifique pour la page de blocage de SeLogerPro (comme dans l'image fournie)
            try:
                # Vérifier si nous sommes sur la page avec un message de blocage
                current_url = page.url

                # Vérification plus précise du contenu de la page pour le message de blocage spécifique
                is_blocked_page = await page.evaluate("""() => {
                    // Vérifier le texte principal pour le message exact de l'image
                    const mainText = document.body.innerText;

                    // Vérifier le message spécifique "Vous avez été bloqué(e)"
                    if (mainText.includes('Vous avez été bloqué(e)')) {
                        return true;
                    }

                    // Vérifier les éléments spécifiques de la page de blocage
                    const hasBlockageText = mainText.includes('Pourquoi ce blocage');
                    const hasPossibilitiesText = mainText.includes('Diverses possibilités');
                    const hasRobotText = mainText.includes('un robot est sur le même réseau');
                    const hasJavascriptText = mainText.includes('quelque chose bloque le fonctionnement de javascript');
                    const hasSpeedText = mainText.includes('vitesse surhumaine');

                    // Si au moins deux de ces éléments sont présents, c'est probablement la page de blocage
                    let count = 0;
                    if (hasBlockageText) count++;
                    if (hasPossibilitiesText) count++;
                    if (hasRobotText) count++;
                    if (hasJavascriptText) count++;
                    if (hasSpeedText) count++;

                    return count >= 2;
                }""")

                if is_blocked_page:
                    self.logger.error("BLOCAGE CRITIQUE DÉTECTÉ: Page de blocage SeLogerPro identifiée")
                    self.logger.error(f"Capture d'écran du blocage enregistrée dans {screenshot_path}")

                    # Extraire l'ID de blocage spécifique à SeLogerPro
                    try:
                        block_id = await page.evaluate("""() => {
                            const idMatch = document.body.innerText.match(/ID: ([a-zA-Z0-9-]+)/);
                            return idMatch ? idMatch[1] : 'Non trouvé';
                        }""")
                        self.logger.error(f"ID de blocage SeLogerPro: {block_id}")
                    except Exception:
                        pass

                    # Extraire l'adresse IP mentionnée dans le message
                    try:
                        ip_address = await page.evaluate("""() => {
                            const ipMatch = document.body.innerText.match(/réseau \(IP ([0-9.]+)\)/);
                            return ipMatch ? ipMatch[1] : 'Non trouvée';
                        }""")
                        self.logger.error(f"Adresse IP mentionnée dans le message de blocage: {ip_address}")
                    except Exception:
                        pass

                    # Message d'erreur critique et arrêt de l'automatisation
                    self.logger.critical("AUTOMATISATION ARRÊTÉE: Blocage critique détecté sur SeLogerPro")
                    self.logger.error("CONSEILS POUR RÉSOUDRE LE BLOCAGE:")
                    self.logger.error("1. Attendez au moins 24 heures avant de réessayer")
                    self.logger.error("2. Utilisez un VPN ou changez d'adresse IP")
                    self.logger.error("3. Essayez depuis un autre appareil ou réseau")
                    self.logger.error("4. Contactez le support de SeLogerPro avec l'ID de blocage")
                    self.logger.error("5. Vérifiez que JavaScript est activé dans votre navigateur")
                    self.logger.error("6. Essayez avec un navigateur différent")

                    return True
            except Exception as specific_error:
                self.logger.debug(f"Erreur lors de la vérification spécifique de blocage SeLogerPro: {specific_error}")

            # Vérifier les messages de blocage courants (liste étendue)
            blocking_messages = [
                "Vous avez été bloqué(e)",
                "You have been blocked",
                "Pourquoi ce blocage",
                "Access denied",
                "Accès refusé",
                "Security check",
                "Vérification de sécurité",
                "Trop de tentatives",
                "Too many attempts",
                "Suspicious activity",
                "Activité suspecte",
                "Robot détecté",
                "Bot detected",
                "Automated access",
                "Accès automatisé",
                "IP has been blocked",
                "Votre IP a été bloquée",
                "Temporarily blocked",
                "Temporairement bloqué",
                "vitesse surhumaine",
                "quelque chose bloque le fonctionnement de javascript",
                "un robot est sur le même réseau",
                "Diverses possibilités",
                "Contactez le support",
                # "ID:", # Retiré car peut être présent sur des pages normales
                "Forbidden",
                "403",
                "Access Denied",
                "Cloudflare",
                "DDoS protection",
                "Ray ID:"
                # "Challenge" # Retiré car peut être présent sur des pages normales avec captcha
            ]

            # Vérifier le contenu de la page pour les messages de blocage
            content = await page.content()
            page_text = await page.evaluate("() => document.body.innerText")

            for message in blocking_messages:
                if message.lower() in content.lower() or message.lower() in page_text.lower():
                    self.logger.error(f"BLOCAGE DÉTECTÉ: Le site a bloqué l'accès. Message: '{message}'")
                    self.logger.error(f"Capture d'écran du blocage enregistrée dans {screenshot_path}")

                    # Extraire l'ID de blocage si disponible
                    try:
                        block_id = await page.evaluate("""
                            () => {
                                const idElement = document.body.innerText.match(/ID:\\s*([a-zA-Z0-9-]+)/i);
                                return idElement ? idElement[1] : 'Non trouvé';
                            }
                        """)
                        self.logger.error(f"ID de blocage: {block_id}")
                    except Exception:
                        pass

                    # Extraire l'adresse IP si disponible
                    try:
                        ip_address = await page.evaluate("""
                            () => {
                                const ipElement = document.body.innerText.match(/IP\\s*([0-9.]+)/i);
                                return ipElement ? ipElement[1] : 'Non trouvée';
                            }
                        """)
                        self.logger.error(f"Adresse IP détectée dans le message: {ip_address}")
                    except Exception:
                        pass

                    # Conseils pour résoudre le problème
                    self.logger.error("CONSEILS POUR RÉSOUDRE LE BLOCAGE:")
                    self.logger.error("1. Attendez au moins 1 heure avant de réessayer")
                    self.logger.error("2. Utilisez un VPN ou changez d'adresse IP")
                    self.logger.error("3. Essayez depuis un autre appareil ou réseau")
                    self.logger.error("4. Contactez le support de SeLogerPro si le problème persiste")
                    self.logger.error("5. Vérifiez que JavaScript est activé dans votre navigateur")

                    return True

            # Vérifier les URL qui indiquent un blocage
            current_url = page.url
            blocking_url_patterns = [
                "blocked", "security-check", "suspicious",
                "403", "forbidden", "denied", "cloudflare",
                "protection", "verify", "bot", "ddos"
            ]

            # Nous avons retiré les motifs suivants car ils peuvent faire partie du processus normal d'authentification:
            # "captcha", "challenge", "human"

            for pattern in blocking_url_patterns:
                if pattern in current_url.lower():
                    self.logger.error(f"BLOCAGE DÉTECTÉ: Redirection vers une page de sécurité: {current_url}")
                    self.logger.error(f"Motif détecté dans l'URL: {pattern}")
                    return True

            # Vérifier les éléments visuels qui indiquent un blocage (liste étendue)
            blocking_selectors = [
                "div:has-text('blocked')",
                "div:has-text('bloqué')",
                "div:has-text('security check')",
                "div:has-text('vérification de sécurité')",
                "div:has-text('suspicious activity')",
                "div:has-text('activité suspecte')",
                "div:has-text('automated access')",
                "div:has-text('accès automatisé')",
                "div:has-text('too many attempts')",
                "div:has-text('trop de tentatives')",
                "div.blocked-message",
                "div.security-check",
                "div.error-message:has-text('blocked')",
                "div.error-message:has-text('bloqué')",
                "div:has-text('Vous avez été bloqué')",
                "div:has-text('You have been blocked')",
                "div:has-text('Pourquoi ce blocage')",
                "div:has-text('robot')",
                "div:has-text('vitesse surhumaine')",
                "div:has-text('javascript')",
                "div:has-text('ID:')",
                "div:has-text('Diverses possibilités')",
                "div:has-text('Contactez le support')",
                "div:has-text('403')",
                "div:has-text('Forbidden')",
                "div:has-text('Access Denied')",
                "div:has-text('Cloudflare')",
                "div:has-text('DDoS')",
                "div:has-text('Ray ID')",
                "div:has-text('Challenge')",
                "iframe[src*='cloudflare']"
                # Nous avons retiré les sélecteurs suivants car ils font partie du processus normal d'authentification:
                # "iframe[src*='captcha']",
                # "iframe[src*='challenge']",
            ]

            for selector in blocking_selectors:
                try:
                    if await page.is_visible(selector, timeout=1000):
                        self.logger.error(f"BLOCAGE DÉTECTÉ: Élément visuel de blocage trouvé avec le sélecteur: {selector}")
                        return True
                except Exception:
                    continue

            # Vérifier si la page est vide ou contient très peu d'éléments (page blanche de blocage)
            try:
                element_count = await page.evaluate("""() => {
                    return document.querySelectorAll('*').length;
                }""")

                if element_count < 10:  # Une page normale a généralement beaucoup plus d'éléments
                    self.logger.warning(f"Page potentiellement bloquée: seulement {element_count} éléments trouvés")
                    return True
            except Exception as e:
                self.logger.debug(f"Erreur lors du comptage des éléments: {e}")

            # Vérifier si la page contient un titre ou un message d'erreur HTTP
            try:
                title = await page.title()
                error_titles = ["403", "Forbidden", "Access Denied", "Error", "Blocked", "Security Check"]

                for error in error_titles:
                    if error.lower() in title.lower():
                        self.logger.error(f"BLOCAGE DÉTECTÉ: Titre de page indiquant un blocage: {title}")
                        return True
            except Exception as e:
                self.logger.debug(f"Erreur lors de la vérification du titre: {e}")

            # Vérifier les codes d'état HTTP
            try:
                response = await page.evaluate("""() => {
                    const performance = window.performance;
                    if (!performance || !performance.getEntries) return null;

                    const entries = performance.getEntries();
                    const navigationEntries = entries.filter(e => e.entryType === 'navigation');

                    if (navigationEntries.length > 0 && navigationEntries[0].responseStatus) {
                        return navigationEntries[0].responseStatus;
                    }
                    return null;
                }""")

                if response and (response == 403 or response == 429 or response == 503):
                    self.logger.error(f"BLOCAGE DÉTECTÉ: Code de réponse HTTP indiquant un blocage: {response}")
                    return True
            except Exception as e:
                self.logger.debug(f"Erreur lors de la vérification du code de réponse HTTP: {e}")

            return False
        except Exception as e:
            self.logger.warning(f"Erreur lors de la vérification du blocage: {e}")
            return False

    async def handle_blocking_detected(self, page: Page) -> bool:
        """Gère la détection d'un blocage et tente des stratégies de récupération"""
        self.logger.error("🚫 BLOCAGE DÉTECTÉ - Activation des stratégies de récupération")

        try:
            # Extraire des informations du blocage
            content = await page.content()

            # Extraire l'ID de blocage s'il existe
            import re
            id_match = re.search(r'id:\s*([a-f0-9-]+)', content.lower())
            if id_match:
                blocking_id = id_match.group(1)
                self.logger.error(f"ID de blocage détecté: {blocking_id}")

            # Extraire l'IP si elle est mentionnée
            ip_match = re.search(r'ip\s+([0-9.]+)', content.lower())
            if ip_match:
                blocked_ip = ip_match.group(1)
                self.logger.error(f"IP bloquée: {blocked_ip}")

            # Sauvegarder le contenu HTML pour analyse
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            html_path = f"screenshots/blocked_content_{timestamp}.html"
            with open(html_path, 'w', encoding='utf-8') as f:
                f.write(content)
            self.logger.info(f"Contenu HTML du blocage sauvegardé: {html_path}")

            # Stratégie 1: Attendre et changer de proxy
            if self.proxy_rotator:
                self.logger.info("Stratégie 1: Rotation du proxy...")
                await self.proxy_rotator.force_rotation()

                # Attendre un délai exponentiel
                wait_time = random.randint(300, 600)  # 5-10 minutes
                self.logger.info(f"Attente de {wait_time} secondes avant nouvelle tentative...")
                await asyncio.sleep(wait_time)

                # Redémarrer le navigateur avec le nouveau proxy
                return False  # Indique qu'il faut redémarrer

            # Stratégie 2: Si pas de proxy, attendre plus longtemps
            else:
                wait_time = random.randint(1800, 3600)  # 30-60 minutes
                self.logger.info(f"Pas de proxy disponible. Attente de {wait_time} secondes...")
                await asyncio.sleep(wait_time)
                return False

        except Exception as e:
            self.logger.error(f"Erreur lors de la gestion du blocage: {e}")
            return False

    async def simulate_human_browsing_before_login(self, page: Page):
        """Simule une navigation humaine avant la connexion"""
        try:
            self.logger.info("Simulation d'une navigation humaine avant connexion...")

            # Aller sur la page d'accueil d'abord
            await page.goto("https://myselogerpro.com", timeout=30000)
            await self._human_like_delay("page_load")

            # Simuler la lecture de la page
            await page.evaluate("""
                () => {
                    // Simuler des mouvements de souris aléatoires
                    let mouseX = Math.random() * window.innerWidth;
                    let mouseY = Math.random() * window.innerHeight;

                    for (let i = 0; i < 5; i++) {
                        setTimeout(() => {
                            mouseX += (Math.random() - 0.5) * 100;
                            mouseY += (Math.random() - 0.5) * 100;
                            mouseX = Math.max(0, Math.min(window.innerWidth, mouseX));
                            mouseY = Math.max(0, Math.min(window.innerHeight, mouseY));

                            const event = new MouseEvent('mousemove', {
                                clientX: mouseX,
                                clientY: mouseY
                            });
                            document.dispatchEvent(event);
                        }, i * 500);
                    }
                }
            """)

            # Faire défiler la page
            await page.evaluate("""
                () => {
                    return new Promise(resolve => {
                        let scrolled = 0;
                        const maxScroll = document.body.scrollHeight - window.innerHeight;
                        const scrollStep = Math.random() * 200 + 100;

                        const scroll = () => {
                            if (scrolled < maxScroll * 0.3) {
                                window.scrollBy(0, scrollStep);
                                scrolled += scrollStep;
                                setTimeout(scroll, Math.random() * 1000 + 500);
                            } else {
                                resolve();
                            }
                        };
                        scroll();
                    });
                }
            """)

            await self._human_like_delay("scroll")

            # Maintenant aller vers la page de connexion
            login_link = await page.query_selector("a[href*='login'], a[href*='connexion'], .login-link")
            if login_link:
                await self._human_like_click(page, login_link)
            else:
                # Si pas de lien trouvé, aller directement à l'URL de connexion
                await page.goto("https://myselogerpro.com/login", timeout=30000)

            await self._human_like_delay("navigation")

        except Exception as e:
            self.logger.warning(f"Erreur lors de la simulation de navigation: {e}")
            # Continuer même en cas d'erreur

    async def login(self, page: Page) -> bool:
        """Effectue la connexion au site"""
        with tracer.start_as_current_span("login"):
            try:
                self.logger.info("Tentative de connexion à SeLogerPro...")

                # Simuler une navigation humaine avant la connexion
                await self.simulate_human_browsing_before_login(page)

                # Vérifier si l'utilisateur est bloqué
                if await self.check_for_blocking(page):
                    # Gérer le blocage détecté
                    should_continue = await self.handle_blocking_detected(page)
                    if not should_continue:
                        return False

                # Simuler un comportement humain sur la page avant toute action
                await self.simulate_human_behavior(page)

                # Gestion des cookies - vérifier si la bannière de cookies est présente
                try:
                    self.logger.info("Vérification de la présence d'une bannière de cookies...")

                    # Attendre que la page soit chargée
                    await page.wait_for_load_state("networkidle", timeout=10000)

                    # Recherche de différents types de bannières de cookies
                    cookie_selectors = [
                        "#didomi-notice-agree-button",  # Bouton Didomi
                        ".didomi-continue-without-agreeing",  # Continuer sans accepter
                        "#onetrust-accept-btn-handler",  # OneTrust
                        ".cookie-banner button[type='submit']",  # Format générique
                        "button:has-text('Accepter')",  # Bouton avec texte Accepter
                        "button:has-text('Accept')",    # Bouton avec texte Accept
                        "button:has-text('cookies')",   # Bouton avec texte cookies
                    ]

                    for selector in cookie_selectors:
                        if await page.is_visible(selector, timeout=1000):
                            self.logger.info(f"Bannière de cookies détectée, acceptation via {selector}")
                            await page.click(selector)
                            await page.wait_for_timeout(1000)  # Attendre que la bannière disparaisse
                            break
                except Exception as cookie_error:
                    self.logger.warning(f"Erreur lors de la gestion des cookies: {cookie_error}")

                # Attendre que la page de connexion soit chargée
                await page.wait_for_load_state("networkidle")

                # Capture d'écran pour débogage
                if self.config["error_handling"]["error_logs"]["screenshot_on_error"]:
                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    await page.screenshot(path=f"screenshots/login_form_before_{timestamp}.png")

                # Sélecteurs spécifiques à SeLogerPro basés sur l'image fournie
                # Utiliser des sélecteurs plus précis et multiples pour augmenter les chances de succès
                email_selectors = [
                    "input[aria-label='Adresse Email ou identifiant client']",
                    "input[placeholder='Adresse Email ou identifiant client']",
                    "input[name='email']",
                    "input[type='email']",
                    "input[id*='email']",
                    "input[id*='username']",
                    "input[id*='login']",
                    "input:first-of-type"  # Dernier recours: premier champ de saisie
                ]

                password_selectors = [
                    "input[aria-label='Mot de passe']",
                    "input[placeholder='Mot de passe']",
                    "input[name='password']",
                    "input[type='password']",
                    "input[id*='password']",
                    "input[id*='pwd']",
                    "input:nth-of-type(2)"  # Dernier recours: deuxième champ de saisie
                ]

                login_button_selectors = [
                    "button:has-text('Me connecter')",
                    "button.btn-primary",
                    "button.btn-connect",
                    "button.login-button",
                    "button[type='submit']",
                    "input[type='submit']",
                    ".btn-connect",
                    ".btn-primary",
                    "button.submit-button",
                    "button:has-text('Connexion')",
                    "button"  # Dernier recours: n'importe quel bouton
                ]

                # Attendre que la page soit complètement chargée
                await page.wait_for_load_state("networkidle", timeout=10000)

                # Afficher le HTML de la page pour débogage
                html_content = await page.content()
                self.logger.debug(f"HTML de la page de connexion (premiers 500 caractères): {html_content[:500]}...")

                # Essayer de trouver le champ email
                email_field = None
                for selector in email_selectors:
                    try:
                        self.logger.debug(f"Essai du sélecteur email: {selector}")
                        email_field = await page.wait_for_selector(selector, timeout=2000)
                        if email_field:
                            self.logger.info(f"Champ email trouvé avec le sélecteur: {selector}")
                            break
                    except Exception:
                        continue

                if not email_field:
                    self.logger.error("Impossible de trouver le champ email")
                    # Capture d'écran pour débogage
                    await page.screenshot(path=f"screenshots/email_field_not_found_{timestamp}.png")
                    return False

                # Essayer de trouver le champ mot de passe
                password_field = None
                for selector in password_selectors:
                    try:
                        self.logger.debug(f"Essai du sélecteur mot de passe: {selector}")
                        password_field = await page.wait_for_selector(selector, timeout=2000)
                        if password_field:
                            self.logger.info(f"Champ mot de passe trouvé avec le sélecteur: {selector}")
                            break
                    except Exception:
                        continue

                if not password_field:
                    self.logger.error("Impossible de trouver le champ mot de passe")
                    # Capture d'écran pour débogage
                    await page.screenshot(path=f"screenshots/password_field_not_found_{timestamp}.png")
                    return False

                # Essayer de trouver le bouton de connexion
                login_button = None
                for selector in login_button_selectors:
                    try:
                        self.logger.debug(f"Essai du sélecteur bouton: {selector}")
                        login_button = await page.wait_for_selector(selector, timeout=2000)
                        if login_button:
                            self.logger.info(f"Bouton de connexion trouvé avec le sélecteur: {selector}")
                            break
                    except Exception:
                        continue

                if not login_button:
                    self.logger.error("Impossible de trouver le bouton de connexion")
                    # Capture d'écran pour débogage
                    await page.screenshot(path=f"screenshots/login_button_not_found_{timestamp}.png")
                    return False

                # Remplissage du formulaire avec variabilité humaine
                self.logger.info("Remplissage du formulaire de connexion...")
                self.logger.info(f"Utilisation du nom d'utilisateur: {self.config['auth']['username']}")

                # Choisir une stratégie de remplissage aléatoire pour être imprévisible
                fill_strategies = ['human_like', 'fast_fill', 'normal_type', 'mixed']
                fill_strategy = random.choice(fill_strategies)
                self.logger.info(f"Stratégie de remplissage sélectionnée: {fill_strategy}")

                # Méthode variable selon la stratégie choisie
                try:
                    username = self.config["auth"]["username"]
                    password = self.config["auth"]["password"]

                    if fill_strategy == 'fast_fill':
                        # Simulation d'un copier-coller ou gestionnaire de mots de passe
                        self.logger.info("Remplissage rapide (simulation copier-coller)")
                        await email_field.fill(username)
                        await page.wait_for_timeout(random.randint(200, 800))

                        # Navigation vers le mot de passe (Tab ou clic)
                        if random.random() > 0.5:
                            await page.keyboard.press('Tab')
                            self.logger.debug("Navigation par Tab vers le mot de passe")
                        else:
                            await password_field.click()
                            self.logger.debug("Navigation par clic vers le mot de passe")

                        await page.wait_for_timeout(random.randint(100, 500))
                        await password_field.fill(password)

                    elif fill_strategy == 'normal_type':
                        # Frappe normale, ni trop lente ni trop rapide
                        self.logger.info("Frappe normale")
                        await email_field.click()
                        await email_field.clear()
                        await email_field.type(username, delay=random.randint(80, 200))

                        await page.wait_for_timeout(random.randint(300, 1000))

                        # Navigation vers le mot de passe
                        if random.random() > 0.3:  # 70% de chance d'utiliser Tab
                            await page.keyboard.press('Tab')
                        else:
                            await password_field.click()

                        await password_field.clear()
                        await password_field.type(password, delay=random.randint(100, 250))

                    elif fill_strategy == 'mixed':
                        # Mélange : email rapide, mot de passe lent (comportement réaliste)
                        self.logger.info("Stratégie mixte : email rapide, mot de passe lent")
                        await email_field.fill(username)  # Rapide pour l'email
                        await page.wait_for_timeout(random.randint(400, 1200))

                        await page.keyboard.press('Tab')  # Tab vers mot de passe
                        await page.wait_for_timeout(random.randint(200, 600))

                        # Mot de passe tapé lentement (plus sécurisé)
                        await password_field.clear()
                        await password_field.type(password, delay=random.randint(150, 350))

                    else:  # human_like (stratégie complexe existante, mais simplifiée)
                        self.logger.info("Frappe humaine détaillée")
                        # Version simplifiée pour human_like
                        await email_field.click()
                        await email_field.clear()

                        # Frappe avec erreurs occasionnelles (plus humain)
                        for i, char in enumerate(username):
                            # Erreur occasionnelle
                            if random.random() < 0.03 and i > 0:  # 3% de chance d'erreur
                                wrong_char = chr(ord(char) + random.randint(1, 3))
                                await email_field.type(wrong_char, delay=random.randint(100, 200))
                                await page.wait_for_timeout(random.randint(200, 400))
                                await email_field.press("Backspace")

                            await email_field.type(char, delay=random.randint(80, 180))

                        await page.wait_for_timeout(random.randint(500, 1200))

                        # Navigation vers mot de passe
                        if random.random() > 0.4:  # 60% de chance d'utiliser Tab
                            await page.keyboard.press('Tab')
                        else:
                            await password_field.click()

                        await page.wait_for_timeout(random.randint(300, 800))
                        await password_field.clear()

                        # Mot de passe tapé plus lentement
                        for char in password:
                            await password_field.type(char, delay=random.randint(120, 250))

                        await page.wait_for_timeout(random.randint(600, 1500))
                except Exception as e:
                    self.logger.warning(f"Erreur lors du remplissage du formulaire avec la méthode 1: {e}")

                    # Méthode 2: Utiliser JavaScript pour remplir les champs
                    self.logger.info("Tentative de remplissage du formulaire avec JavaScript...")
                    try:
                        await page.evaluate("""
                            (username, password) => {
                                // Trouver tous les champs de saisie
                                const inputs = document.querySelectorAll('input');

                                // Chercher le champ email/username
                                let emailField = null;
                                let passwordField = null;

                                for (const input of inputs) {
                                    const type = input.type.toLowerCase();
                                    const name = (input.name || '').toLowerCase();
                                    const placeholder = (input.placeholder || '').toLowerCase();
                                    const ariaLabel = (input.getAttribute('aria-label') || '').toLowerCase();

                                    // Identifier le champ email
                                    if (type === 'email' ||
                                        name.includes('email') ||
                                        name.includes('login') ||
                                        name.includes('username') ||
                                        placeholder.includes('email') ||
                                        placeholder.includes('identifiant') ||
                                        ariaLabel.includes('email') ||
                                        ariaLabel.includes('identifiant')) {
                                        emailField = input;
                                    }

                                    // Identifier le champ mot de passe
                                    if (type === 'password' ||
                                        name.includes('password') ||
                                        name.includes('pwd') ||
                                        placeholder.includes('mot de passe') ||
                                        ariaLabel.includes('mot de passe')) {
                                        passwordField = input;
                                    }
                                }

                                // Remplir les champs si trouvés
                                if (emailField) {
                                    emailField.value = username;
                                }

                                if (passwordField) {
                                    passwordField.value = password;
                                }
                            }
                        """, self.config["auth"]["username"], self.config["auth"]["password"])

                        self.logger.info("Formulaire rempli avec JavaScript")
                    except Exception as js_error:
                        self.logger.error(f"Erreur lors du remplissage du formulaire avec JavaScript: {js_error}")

                        # Méthode 3: Dernier recours - utiliser fill directement
                        self.logger.info("Tentative de remplissage direct des champs...")
                        try:
                            for selector in email_selectors:
                                try:
                                    await page.fill(selector, self.config["auth"]["username"])
                                    self.logger.info(f"Champ email rempli avec le sélecteur: {selector}")
                                    break
                                except Exception:
                                    continue

                            for selector in password_selectors:
                                try:
                                    await page.fill(selector, self.config["auth"]["password"])
                                    self.logger.info(f"Champ mot de passe rempli avec le sélecteur: {selector}")
                                    break
                                except Exception:
                                    continue
                        except Exception as fill_error:
                            self.logger.error(f"Échec de toutes les méthodes de remplissage: {fill_error}")

                # Capture d'écran après remplissage
                if self.config["error_handling"]["error_logs"]["screenshot_on_error"]:
                    await page.screenshot(path=f"screenshots/login_form_filled_{timestamp}.png")

                # Soumission du formulaire
                self.logger.info("Soumission du formulaire de connexion...")

                # Capture d'écran avant de cliquer sur le bouton
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                await page.screenshot(path=f"screenshots/before_submit_{timestamp}.png")

                # Essayer plusieurs méthodes pour cliquer sur le bouton
                try:
                    # Méthode 1: Clic standard avec comportement humain
                    self.logger.info("Tentative de clic standard sur le bouton avec comportement humain...")

                    # Obtenir les coordonnées du bouton
                    button_box = await login_button.bounding_box()
                    if button_box:
                        # Calculer le centre du bouton
                        center_x = button_box["x"] + button_box["width"] / 2
                        center_y = button_box["y"] + button_box["height"] / 2

                        # Ajouter une légère variation aléatoire pour ne pas cliquer exactement au centre
                        offset_x = random.uniform(-button_box["width"] * 0.2, button_box["width"] * 0.2)
                        offset_y = random.uniform(-button_box["height"] * 0.2, button_box["height"] * 0.2)

                        click_x = center_x + offset_x
                        click_y = center_y + offset_y

                        # Déplacer la souris vers le bouton de manière naturelle
                        current_x, current_y = await page.evaluate("() => { return [window.mouseX || 0, window.mouseY || 0]; }")

                        # Si les coordonnées actuelles ne sont pas disponibles, utiliser une position par défaut
                        if not current_x or not current_y:
                            current_x = random.randint(0, await page.evaluate("window.innerWidth"))
                            current_y = random.randint(0, await page.evaluate("window.innerHeight"))

                        # Déplacer la souris avec une courbe naturelle
                        steps = random.randint(15, 25)

                        # Fonction pour créer une courbe de mouvement plus naturelle
                        def ease_out_cubic(t):
                            return 1 - pow(1 - t, 3)

                        # Mouvement de la souris vers le bouton
                        for i in range(1, steps + 1):
                            progress = i / steps
                            eased_progress = ease_out_cubic(progress)

                            # Calculer la position intermédiaire avec une légère courbe
                            curve_x = random.uniform(-10, 10) * math.sin(progress * math.pi)
                            curve_y = random.uniform(-10, 10) * math.sin(progress * math.pi)

                            pos_x = current_x + (click_x - current_x) * eased_progress + curve_x * (1 - abs(2 * progress - 1))
                            pos_y = current_y + (click_y - current_y) * eased_progress + curve_y * (1 - abs(2 * progress - 1))

                            await page.mouse.move(pos_x, pos_y, steps=random.randint(1, 3))

                            # Pause variable entre les mouvements
                            if progress < 0.2 or progress > 0.8:
                                await page.wait_for_timeout(random.randint(10, 30))
                            else:
                                await page.wait_for_timeout(random.randint(5, 15))

                        # Pause avant de cliquer (comme si on hésitait)
                        await page.wait_for_timeout(random.randint(300, 800))

                        # Cliquer sur le bouton
                        await page.mouse.click(click_x, click_y)
                        self.logger.info(f"Clic sur le bouton aux coordonnées ({click_x}, {click_y})")
                    else:
                        # Fallback si on ne peut pas obtenir les coordonnées
                        self.logger.info("Impossible d'obtenir les coordonnées du bouton, utilisation du clic standard")
                        await login_button.click(force=True, timeout=5000)
                except Exception as e:
                    self.logger.warning(f"Erreur lors du clic standard: {e}")

                    # Méthode 2: Clic via JavaScript
                    self.logger.info("Tentative de clic via JavaScript...")
                    try:
                        await page.evaluate("""
                            () => {
                                // Essayer de trouver le bouton de connexion
                                const buttons = Array.from(document.querySelectorAll('button'));
                                const loginButton = buttons.find(button =>
                                    button.textContent.includes('Me connecter') ||
                                    button.classList.contains('btn-primary') ||
                                    button.classList.contains('btn-connect') ||
                                    button.classList.contains('login-button')
                                );

                                if (loginButton) {
                                    console.log('Bouton trouvé via JS, clic...');
                                    loginButton.click();
                                    return true;
                                }

                                // Si on ne trouve pas le bouton spécifique, chercher n'importe quel bouton de type submit
                                const submitButton = document.querySelector('button[type="submit"], input[type="submit"]');
                                if (submitButton) {
                                    console.log('Bouton submit trouvé via JS, clic...');
                                    submitButton.click();
                                    return true;
                                }

                                return false;
                            }
                        """)
                    except Exception as js_error:
                        self.logger.warning(f"Erreur lors du clic via JavaScript: {js_error}")

                        # Méthode 3: Soumettre le formulaire directement
                        self.logger.info("Tentative de soumission directe du formulaire...")
                        try:
                            await page.evaluate("""
                                () => {
                                    const form = document.querySelector('form');
                                    if (form) {
                                        console.log('Formulaire trouvé, soumission...');
                                        form.submit();
                                        return true;
                                    }
                                    return false;
                                }
                            """)
                        except Exception as form_error:
                            self.logger.error(f"Erreur lors de la soumission du formulaire: {form_error}")

                # Capture d'écran après avoir cliqué sur le bouton
                await page.wait_for_timeout(1000)  # Attendre un peu pour que la page réagisse
                await page.screenshot(path=f"screenshots/after_submit_{timestamp}.png")

                # Attendre un peu pour voir si une page de blocage apparaît immédiatement
                await page.wait_for_timeout(2000)

                # Vérification précoce de blocage (avant même le chargement complet)
                if await self.check_for_blocking(page):
                    self.logger.error("BLOCAGE DÉTECTÉ immédiatement après clic sur le bouton de connexion")
                    await page.screenshot(path=f"screenshots/blocked_after_click_{timestamp}.png")

                    # Vérifier si c'est le blocage critique spécifique de SeLogerPro
                    current_url = page.url
                    if "myselogerpro.com/login" in current_url:
                        try:
                            # Vérifier si c'est la page de blocage spécifique
                            is_critical_block = await page.evaluate("""() => {
                                return document.body.innerText.includes('Vous avez été bloqué(e)') &&
                                       document.body.innerText.includes('Pourquoi ce blocage');
                            }""")

                            if is_critical_block:
                                self.logger.critical("ARRÊT DE L'AUTOMATISATION: Blocage critique détecté immédiatement après clic")
                                # Prendre une capture d'écran finale du blocage
                                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                                await page.screenshot(path=f"screenshots/critical_block_immediate_{timestamp}.png")
                                # Lever une exception pour arrêter complètement l'automatisation
                                raise Exception("BLOCAGE CRITIQUE: Automatisation arrêtée - Vous avez été bloqué(e) par SeLogerPro")
                        except Exception as e:
                            if "BLOCAGE CRITIQUE" in str(e):
                                raise  # Re-lever l'exception pour arrêter l'automatisation

                    return False

                # Attendre la redirection ou le chargement de la page avec un timeout plus long
                self.logger.info("Attente du chargement complet de la page après connexion...")
                try:
                    # Attendre d'abord que le DOM soit chargé (plus rapide et plus fiable)
                    await page.wait_for_load_state("domcontentloaded", timeout=30000)
                    self.logger.info("Page chargée (domcontentloaded)")

                    # Attendre un peu plus pour que les scripts se chargent
                    await page.wait_for_timeout(2000)

                    # Vérifier si la page est bloquée
                    if await self.check_for_blocking(page):
                        self.logger.error("BLOCAGE DÉTECTÉ lors du chargement après connexion")
                        return False

                    # Capture d'écran pour débogage
                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    await page.screenshot(path=f"screenshots/page_loaded_after_login_{timestamp}.png")

                    # Attendre que le réseau soit inactif, mais avec un timeout plus court pour éviter de bloquer
                    try:
                        await page.wait_for_load_state("networkidle", timeout=10000)
                        self.logger.info("Réseau inactif (networkidle)")
                    except Exception as net_e:
                        self.logger.warning(f"Timeout sur networkidle, mais la page est chargée: {net_e}")
                        # Continuer même si networkidle n'est pas atteint
                except Exception as e:
                    self.logger.error(f"Erreur lors de l'attente du chargement de la page après connexion: {e}")
                    # Gérer les erreurs de proxy
                    await self._handle_proxy_error(e)
                    # Continuer malgré l'erreur, car la page pourrait être partiellement chargée

                # Vérifier si l'utilisateur est bloqué après la tentative de connexion
                if await self.check_for_blocking(page):
                    self.logger.error("Blocage détecté après tentative de connexion")

                    # Vérifier si c'est le blocage critique spécifique de SeLogerPro
                    current_url = page.url
                    if "myselogerpro.com/login" in current_url:
                        try:
                            # Vérifier si c'est la page de blocage spécifique
                            is_critical_block = await page.evaluate("""() => {
                                return document.body.innerText.includes('Vous avez été bloqué(e)') &&
                                       document.body.innerText.includes('Pourquoi ce blocage');
                            }""")

                            if is_critical_block:
                                self.logger.critical("ARRÊT DE L'AUTOMATISATION: Blocage critique détecté pendant la connexion")
                                # Prendre une capture d'écran finale du blocage
                                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                                await page.screenshot(path=f"screenshots/critical_block_login_{timestamp}.png")
                                # Lever une exception pour arrêter complètement l'automatisation
                                raise Exception("BLOCAGE CRITIQUE: Automatisation arrêtée - Vous avez été bloqué(e) par SeLogerPro")
                        except Exception as e:
                            if "BLOCAGE CRITIQUE" in str(e):
                                raise  # Re-lever l'exception pour arrêter l'automatisation

                    return False

                # Vérifier s'il y a un captcha
                captcha_selectors = [
                    ".captcha-container",
                    "iframe[src*='captcha']",
                    "iframe[src*='recaptcha']",
                    ".g-recaptcha",
                    "div[class*='captcha']",
                    "div:has-text('Verification Required')",
                    "div:has-text('Slide right to complete')",
                    "div:has-text('Slide right')",
                    "div:has-text('puzzle')",
                    "div.slider-captcha",
                    ".slider-puzzle",
                    "button.slider-btn",
                    "div.puzzle-slider"
                ]

                # Capture d'écran pour vérifier la présence d'un captcha
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                await page.screenshot(path=f"screenshots/after_login_check_{timestamp}.png")

                # Vérifier si un captcha est présent
                captcha_detected = False
                slider_captcha = False
                captcha_element = None
                captcha_expired = False

                # Capture d'écran pour vérifier visuellement l'état du captcha
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                await page.screenshot(path=f"screenshots/captcha_check_initial_{timestamp}.png")

                # Vérifier d'abord si le captcha a expiré
                refresh_button_selectors = [
                    "button.refresh-button",
                    "button.captcha-refresh",
                    ".refresh-icon",
                    "button:has-text('Réessayer')",
                    "button:has-text('RÉESSAYER')",
                    "img[alt='refresh']",
                    ".captcha-refresh-icon"
                ]

                # Vérifier si un bouton de rafraîchissement est visible
                for selector in refresh_button_selectors:
                    try:
                        if await page.is_visible(selector, timeout=1000):
                            captcha_expired = True
                            captcha_detected = True
                            self.logger.info(f"Bouton de rafraîchissement du captcha détecté avec le sélecteur: {selector}")
                            # Cliquer sur le bouton de rafraîchissement
                            await page.click(selector)
                            self.logger.info("Clic sur le bouton de rafraîchissement du captcha")
                            await page.wait_for_timeout(2000)  # Attendre que le nouveau captcha se charge

                            # Capture d'écran après le rafraîchissement
                            await page.screenshot(path=f"screenshots/captcha_after_refresh_initial_{timestamp}.png")
                            break
                    except Exception as e:
                        self.logger.debug(f"Erreur lors de la vérification du bouton de rafraîchissement {selector}: {e}")

                # Si aucun bouton de rafraîchissement n'est trouvé, vérifier si l'image du captcha contient une icône de rafraîchissement
                if not captcha_expired:
                    captcha_image_selectors = [
                        "img[src*='captcha']",
                        "iframe[src*='captcha']",
                        ".captcha-image",
                        ".captcha-container img"
                    ]

                    for selector in captcha_image_selectors:
                        try:
                            if await page.is_visible(selector, timeout=1000):
                                captcha_detected = True
                                # Vérifier si l'image contient une icône de rafraîchissement (comme dans l'image fournie)
                                element = await page.query_selector(selector)
                                if element:
                                    box = await element.bounding_box()
                                    if box:
                                        # Prendre une capture d'écran de l'élément
                                        await page.screenshot(path=f"screenshots/captcha_element_{timestamp}.png", clip=box)

                                        # Cliquer au centre de l'image du captcha pour le rafraîchir
                                        center_x = box["x"] + box["width"] / 2
                                        center_y = box["y"] + box["height"] / 2
                                        await page.mouse.click(center_x, center_y)
                                        self.logger.info("Clic au centre de l'image du captcha pour le rafraîchir")
                                        captcha_expired = True
                                        await page.wait_for_timeout(2000)  # Attendre que le nouveau captcha se charge

                                        # Capture d'écran après le rafraîchissement
                                        await page.screenshot(path=f"screenshots/captcha_after_center_click_{timestamp}.png")
                                        break
                        except Exception as e:
                            self.logger.debug(f"Erreur lors de la vérification de l'image du captcha {selector}: {e}")

                # Si le captcha a été rafraîchi, attendre un peu et continuer la détection
                if captcha_expired:
                    self.logger.info("Le captcha a été rafraîchi, vérification du nouveau captcha...")
                    await page.wait_for_timeout(2000)  # Attendre que le nouveau captcha se charge complètement

                # Continuer avec la détection normale du captcha
                for captcha_selector in captcha_selectors:
                    try:
                        if await page.is_visible(captcha_selector, timeout=1000):
                            captcha_detected = True
                            captcha_element = await page.query_selector(captcha_selector)
                            self.logger.warning(f"Captcha détecté avec le sélecteur: {captcha_selector}")

                            # Vérifier si c'est un captcha slider
                            slider_selectors = [
                                "button.slider-btn",
                                ".slider-button",
                                ".slider-handle",
                                "div.slider",
                                "div[role='slider']",
                                "div.sliderContainer",
                                "div.slider-container"
                            ]

                            # Vérifier d'abord dans le contenu principal
                            for slider_selector in slider_selectors:
                                if await page.is_visible(slider_selector, timeout=1000):
                                    slider_captcha = True
                                    self.logger.info(f"Captcha slider détecté avec le sélecteur: {slider_selector}")
                                    break

                            # Si aucun slider n'est trouvé, vérifier dans les iframes
                            if not slider_captcha and await page.is_visible("iframe[src*='captcha']", timeout=1000):
                                self.logger.info("Captcha dans iframe détecté, tentative d'analyse...")

                                # Obtenir tous les iframes
                                iframes = await page.query_selector_all("iframe[src*='captcha']")

                                for iframe in iframes:
                                    try:
                                        # Obtenir le contenu de l'iframe
                                        frame = await iframe.content_frame()
                                        if frame:
                                            # Vérifier les sélecteurs de slider dans l'iframe
                                            for slider_selector in slider_selectors:
                                                if await frame.is_visible(slider_selector, timeout=1000):
                                                    slider_captcha = True
                                                    self.logger.info(f"Captcha slider détecté dans iframe avec le sélecteur: {slider_selector}")
                                                    break
                                    except Exception as e:
                                        self.logger.debug(f"Erreur lors de l'analyse de l'iframe: {e}")

                                # Si toujours pas détecté comme slider mais c'est un iframe captcha,
                                # on le considère comme un slider pour essayer 2Captcha
                                if not slider_captcha:
                                    self.logger.info("Iframe captcha détecté, on le traite comme un slider pour 2Captcha")
                                    slider_captcha = True

                            # Si un captcha est trouvé, sortir de la boucle
                            break
                    except Exception as e:
                        self.logger.debug(f"Erreur lors de la vérification du sélecteur {captcha_selector}: {e}")

                # Si un captcha est détecté
                if captcha_detected:
                    self.logger.warning("Captcha détecté après la connexion.")

                    # Capture d'écran du captcha
                    await page.screenshot(path=f"screenshots/captcha_detected_{timestamp}.png")

                    # Si c'est un captcha slider, essayer de le résoudre
                    if slider_captcha:
                        self.logger.info("Tentative de résolution du captcha slider...")

                        # Vérifier si 2Captcha est configuré
                        if self.config.get("auth", {}).get("captcha", {}).get("service") == "2captcha":
                            self.logger.info("Utilisation de 2Captcha pour résoudre le captcha slider...")

                        captcha_solved = await self._solve_slider_captcha(page)

                        if captcha_solved:
                            self.logger.info("Captcha slider résolu avec succès!")
                            # Attendre que la page se charge après la résolution du captcha
                            await page.wait_for_load_state("networkidle", timeout=10000)

                            # Vérifier si le site demande à nouveau les identifiants
                            self.logger.info("Vérification si une nouvelle connexion est nécessaire après le captcha...")

                            # Attendre un peu pour que la page se stabilise
                            await page.wait_for_timeout(2000)

                            # Vérifier si nous sommes toujours sur la page de connexion
                            login_form_visible = False

                            # Vérifier la présence du formulaire de connexion
                            login_form_selectors = [
                                "input[type='password']",
                                "button:has-text('Me connecter')",
                                "button:has-text('Connexion')",
                                "form.login-form"
                            ]

                            for selector in login_form_selectors:
                                if await page.is_visible(selector, timeout=1000):
                                    login_form_visible = True
                                    self.logger.info(f"Formulaire de connexion détecté après captcha avec le sélecteur: {selector}")
                                    break

                            if login_form_visible:
                                self.logger.info("Nouvelle connexion nécessaire après résolution du captcha")

                                # Remplir à nouveau le formulaire de connexion
                                email_selectors = [
                                    "input[aria-label='Adresse Email ou identifiant client']",
                                    "input[placeholder='Adresse Email ou identifiant client']",
                                    "input[name='email']",
                                    "input[type='email']",
                                    "input[id*='email']",
                                    "input[id*='username']",
                                    "input[id*='login']",
                                    "input:first-of-type"
                                ]

                                password_selectors = [
                                    "input[aria-label='Mot de passe']",
                                    "input[placeholder='Mot de passe']",
                                    "input[name='password']",
                                    "input[type='password']",
                                    "input[id*='password']",
                                    "input[id*='pwd']",
                                    "input:nth-of-type(2)"
                                ]

                                login_button_selectors = [
                                    "button:has-text('Me connecter')",
                                    "button.btn-primary",
                                    "button.btn-connect",
                                    "button.login-button",
                                    "button[type='submit']",
                                    "input[type='submit']",
                                    ".btn-connect",
                                    ".btn-primary",
                                    "button.submit-button",
                                    "button:has-text('Connexion')",
                                    "button"
                                ]

                                # Remplir le formulaire avec un comportement humain simplifié mais efficace
                                self.logger.info("Remplissage du formulaire de connexion après captcha...")

                                # Capture d'écran pour débogage
                                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                                await page.screenshot(path=f"screenshots/form_after_captcha_{timestamp}.png")

                                # Trouver et remplir le champ email
                                email_filled = False
                                for selector in email_selectors:
                                    try:
                                        if await page.is_visible(selector, timeout=1000):
                                            # Vider le champ d'abord
                                            await page.click(selector, click_count=3)
                                            await page.press(selector, "Backspace")

                                            # Pause aléatoire avant de commencer à taper
                                            await page.wait_for_timeout(random.randint(300, 800))

                                            # Taper le nom d'utilisateur avec des délais variables
                                            await page.type(selector, self.config["auth"]["username"], delay=random.randint(100, 200))

                                            self.logger.info(f"Champ email rempli avec le sélecteur: {selector}")
                                            email_filled = True
                                            break
                                    except Exception as e:
                                        self.logger.debug(f"Erreur avec le sélecteur email {selector}: {e}")
                                        continue

                                if not email_filled:
                                    self.logger.warning("Impossible de remplir le champ email, tentative avec JavaScript...")
                                    try:
                                        await page.evaluate(f"""
                                            () => {{
                                                const inputs = document.querySelectorAll('input');
                                                for (const input of inputs) {{
                                                    if (input.type === 'email' ||
                                                        input.type === 'text' ||
                                                        (input.placeholder && input.placeholder.toLowerCase().includes('email')) ||
                                                        (input.name && input.name.toLowerCase().includes('email'))) {{
                                                        input.value = '{self.config["auth"]["username"]}';
                                                        return true;
                                                    }}
                                                }}
                                                return false;
                                            }}
                                        """)
                                        self.logger.info("Champ email rempli avec JavaScript")
                                        email_filled = True
                                    except Exception as js_error:
                                        self.logger.error(f"Erreur lors du remplissage du champ email avec JavaScript: {js_error}")

                                # Pause entre les champs
                                await page.wait_for_timeout(random.randint(500, 1000))

                                # Trouver et remplir le champ mot de passe
                                password_filled = False
                                for selector in password_selectors:
                                    try:
                                        if await page.is_visible(selector, timeout=1000):
                                            # Vider le champ d'abord
                                            await page.click(selector, click_count=3)
                                            await page.press(selector, "Backspace")

                                            # Pause aléatoire avant de commencer à taper
                                            await page.wait_for_timeout(random.randint(300, 800))

                                            # Taper le mot de passe avec des délais variables
                                            await page.type(selector, self.config["auth"]["password"], delay=random.randint(150, 250))

                                            self.logger.info(f"Champ mot de passe rempli avec le sélecteur: {selector}")
                                            password_filled = True
                                            break
                                    except Exception as e:
                                        self.logger.debug(f"Erreur avec le sélecteur mot de passe {selector}: {e}")
                                        continue

                                if not password_filled:
                                    self.logger.warning("Impossible de remplir le champ mot de passe, tentative avec JavaScript...")
                                    try:
                                        await page.evaluate(f"""
                                            () => {{
                                                const inputs = document.querySelectorAll('input');
                                                for (const input of inputs) {{
                                                    if (input.type === 'password') {{
                                                        input.value = '{self.config["auth"]["password"]}';
                                                        return true;
                                                    }}
                                                }}
                                                return false;
                                            }}
                                        """)
                                        self.logger.info("Champ mot de passe rempli avec JavaScript")
                                        password_filled = True
                                    except Exception as js_error:
                                        self.logger.error(f"Erreur lors du remplissage du champ mot de passe avec JavaScript: {js_error}")

                                # Vérifier si les deux champs ont été remplis
                                if not email_filled or not password_filled:
                                    self.logger.error("Impossible de remplir les champs de connexion après captcha")
                                    # Dernier recours - utiliser fill directement
                                    try:
                                        for selector in email_selectors:
                                            try:
                                                if await page.is_visible(selector, timeout=1000):
                                                    await page.fill(selector, self.config["auth"]["username"])
                                                    self.logger.info(f"Champ email rempli avec fill et le sélecteur: {selector}")
                                                    email_filled = True
                                                    break
                                            except Exception:
                                                continue

                                        for selector in password_selectors:
                                            try:
                                                if await page.is_visible(selector, timeout=1000):
                                                    await page.fill(selector, self.config["auth"]["password"])
                                                    self.logger.info(f"Champ mot de passe rempli avec fill et le sélecteur: {selector}")
                                                    password_filled = True
                                                    break
                                            except Exception:
                                                continue
                                    except Exception as fill_error:
                                        self.logger.error(f"Échec de toutes les méthodes de remplissage après captcha: {fill_error}")

                                # Pause finale avant de cliquer sur le bouton
                                await page.wait_for_timeout(random.randint(500, 1000))

                                # Cliquer sur le bouton de connexion avec un comportement humain simplifié
                                self.logger.info("Clic sur le bouton de connexion après captcha...")

                                # Capture d'écran avant de cliquer sur le bouton
                                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                                await page.screenshot(path=f"screenshots/before_submit_after_captcha_{timestamp}.png")

                                # Essayer de cliquer sur le bouton de connexion
                                button_clicked = False
                                for selector in login_button_selectors:
                                    try:
                                        if await page.is_visible(selector, timeout=1000):
                                            # Pause avant de cliquer (comme si on réfléchissait)
                                            await page.wait_for_timeout(random.randint(500, 1000))

                                            # Cliquer sur le bouton avec un comportement humain simplifié
                                            login_button = await page.query_selector(selector)
                                            if login_button:
                                                # Obtenir les dimensions du bouton
                                                button_box = await login_button.bounding_box()
                                                if button_box:
                                                    # Calculer le centre du bouton avec une légère variation
                                                    center_x = button_box["x"] + button_box["width"] / 2
                                                    center_y = button_box["y"] + button_box["height"] / 2

                                                    # Ajouter une légère variation aléatoire
                                                    offset_x = random.uniform(-button_box["width"] * 0.2, button_box["width"] * 0.2)
                                                    offset_y = random.uniform(-button_box["height"] * 0.2, button_box["height"] * 0.2)

                                                    click_x = center_x + offset_x
                                                    click_y = center_y + offset_y

                                                    # Déplacer la souris vers le bouton
                                                    await page.mouse.move(click_x, click_y, steps=random.randint(5, 10))
                                                    await page.wait_for_timeout(random.randint(100, 300))

                                                    # Cliquer sur le bouton
                                                    await page.mouse.click(click_x, click_y)
                                                    self.logger.info(f"Clic sur le bouton aux coordonnées ({click_x}, {click_y}) avec le sélecteur: {selector}")
                                                else:
                                                    # Fallback si on ne peut pas obtenir les coordonnées
                                                    await login_button.click(force=True)
                                                    self.logger.info(f"Clic standard sur le bouton avec le sélecteur: {selector}")
                                            else:
                                                # Fallback si on ne peut pas obtenir le bouton
                                                await page.click(selector)
                                                self.logger.info(f"Clic direct sur le sélecteur: {selector}")

                                            button_clicked = True
                                            break
                                    except Exception as e:
                                        self.logger.debug(f"Erreur lors du clic sur le bouton avec le sélecteur {selector}: {e}")
                                        continue

                                # Si aucun bouton n'a été cliqué, essayer d'autres méthodes
                                if not button_clicked:
                                    # Méthode 2: Clic via JavaScript
                                    self.logger.info("Tentative de clic via JavaScript après captcha...")
                                    try:
                                        await page.evaluate("""
                                            () => {
                                                // Essayer de trouver le bouton de connexion
                                                const buttons = Array.from(document.querySelectorAll('button'));
                                                const loginButton = buttons.find(button =>
                                                    button.textContent.includes('Me connecter') ||
                                                    button.textContent.includes('Connexion') ||
                                                    button.classList.contains('btn-primary') ||
                                                    button.classList.contains('btn-connect') ||
                                                    button.classList.contains('login-button')
                                                );

                                                if (loginButton) {
                                                    console.log('Bouton trouvé via JS, clic...');
                                                    loginButton.click();
                                                    return true;
                                                }

                                                // Si on ne trouve pas le bouton spécifique, chercher n'importe quel bouton de type submit
                                                const submitButton = document.querySelector('button[type="submit"], input[type="submit"]');
                                                if (submitButton) {
                                                    console.log('Bouton submit trouvé via JS, clic...');
                                                    submitButton.click();
                                                    return true;
                                                }

                                                // Dernier recours: soumettre le formulaire
                                                const form = document.querySelector('form');
                                                if (form) {
                                                    console.log('Formulaire trouvé, soumission...');
                                                    form.submit();
                                                    return true;
                                                }

                                                return false;
                                            }
                                        """)
                                        self.logger.info("Action via JavaScript effectuée après captcha")
                                        button_clicked = True
                                    except Exception as js_error:
                                        self.logger.warning(f"Erreur lors de l'action via JavaScript après captcha: {js_error}")

                                # Capture d'écran après avoir cliqué sur le bouton
                                await page.wait_for_timeout(1000)  # Attendre un peu pour que la page réagisse
                                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                                await page.screenshot(path=f"screenshots/after_submit_after_captcha_{timestamp}.png")

                                # Vérification précoce de blocage, mais on ignore les captchas normaux
                                # Vérifier si c'est un vrai blocage et non un captcha normal
                                current_url = page.url
                                if "login" not in current_url and await self.check_for_blocking(page):
                                    # Vérifier si c'est un vrai blocage ou juste un captcha normal
                                    try:
                                        # Vérifier si la page contient des messages de blocage spécifiques
                                        page_text = await page.evaluate("() => document.body.innerText")
                                        blocking_messages = [
                                            "Vous avez été bloqué(e)",
                                            "Pourquoi ce blocage",
                                            "Diverses possibilités",
                                            "un robot est sur le même réseau",
                                            "vitesse surhumaine"
                                        ]

                                        is_real_block = False
                                        for message in blocking_messages:
                                            if message.lower() in page_text.lower():
                                                is_real_block = True
                                                break

                                        if is_real_block:
                                            self.logger.error("BLOCAGE RÉEL DÉTECTÉ après clic sur le bouton de connexion après captcha")
                                            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                                            await page.screenshot(path=f"screenshots/blocked_after_click_after_captcha_{timestamp}.png")
                                            return False
                                        else:
                                            self.logger.info("Fausse alerte de blocage - c'est probablement un captcha normal")
                                    except Exception as e:
                                        self.logger.debug(f"Erreur lors de la vérification du blocage réel: {e}")
                                        # En cas d'erreur, on continue

                                # Attendre que la page se charge après la connexion post-captcha
                                self.logger.info("Attente du chargement complet de la page après connexion post-captcha...")
                                try:
                                    # Attendre d'abord que le DOM soit chargé (plus rapide et plus fiable)
                                    await page.wait_for_load_state("domcontentloaded", timeout=30000)
                                    self.logger.info("Page chargée après captcha (domcontentloaded)")

                                    # Attendre un peu plus pour que les scripts se chargent
                                    await page.wait_for_timeout(2000)

                                    # Capture d'écran pour débogage
                                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                                    await page.screenshot(path=f"screenshots/page_loaded_after_captcha_{timestamp}.png")

                                    # Attendre que le réseau soit inactif, mais avec un timeout plus court pour éviter de bloquer
                                    try:
                                        await page.wait_for_load_state("networkidle", timeout=10000)
                                        self.logger.info("Réseau inactif après captcha (networkidle)")
                                    except Exception as net_e:
                                        self.logger.warning(f"Timeout sur networkidle après captcha, mais la page est chargée: {net_e}")
                                        # Continuer même si networkidle n'est pas atteint
                                except Exception as e:
                                    self.logger.error(f"Erreur lors de l'attente du chargement de la page après captcha: {e}")
                                    # Continuer malgré l'erreur, car la page pourrait être partiellement chargée

                                # Vérification simplifiée après la connexion post-captcha
                                self.logger.info("Vérification après connexion post-captcha...")

                                # Capture d'écran pour analyse
                                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                                await page.screenshot(path=f"screenshots/verification_login_after_captcha_{timestamp}.png")

                                # Vérifier d'abord si nous sommes bloqués
                                if await self.check_for_blocking(page):
                                    self.logger.error("BLOCAGE DÉTECTÉ après connexion post-captcha")
                                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                                    await page.screenshot(path=f"screenshots/blocked_after_captcha_login_{timestamp}.png")
                                    return False

                                # Vérifier si nous sommes connectés
                                current_url = page.url
                                self.logger.info(f"URL actuelle après connexion post-captcha: {current_url}")

                                # Vérification simple basée sur l'URL
                                if "myselogerpro.com" in current_url and "login" not in current_url:
                                    self.logger.info(f"Connexion après captcha réussie! URL indiquant le succès: {current_url}")

                                    # Sauvegarder l'état d'authentification pour les futures exécutions
                                    try:
                                        # Attendre un peu pour s'assurer que tous les cookies sont définis
                                        await page.wait_for_timeout(2000)
                                        browser_context = page.context
                                        await self.save_authentication_state(browser_context, self.config["auth"]["password"])
                                        self.logger.info("État d'authentification sauvegardé après connexion post-captcha")
                                    except Exception as e:
                                        self.logger.warning(f"Erreur lors de la sauvegarde de l'état d'authentification après captcha: {e}")

                                    return True

                                # Si nous sommes toujours sur la page de connexion, c'est un échec
                                if "login" in current_url:
                                    self.logger.error("Échec de la connexion après captcha: toujours sur la page de connexion")
                                    return False

                                # Vérification supplémentaire basée sur des éléments visuels
                                success_selectors = [
                                    ".user-profile", ".dashboard", ".logged-in",
                                    "a:has-text('Déconnexion')", "a:has-text('Mon compte')",
                                    "div:has-text('Bienvenue')", "div:has-text('Découvrir nos offres')"
                                ]

                                for selector in success_selectors:
                                    try:
                                        if await page.is_visible(selector, timeout=1000):
                                            self.logger.info(f"Connexion après captcha réussie! Indicateur visuel trouvé: {selector}")
                                            return True
                                    except Exception:
                                        continue

                                # Si nous ne sommes pas sur la page de connexion mais n'avons pas trouvé d'indicateurs de succès,
                                # supposons que la connexion a réussi mais que la page est différente de ce que nous attendions
                                self.logger.warning("Aucun indicateur de succès trouvé, mais pas sur la page de connexion. Supposons que la connexion a réussi.")
                                return True
                        else:
                            self.logger.error("Échec de la résolution automatique du captcha slider.")

                            # En mode visible, proposer une résolution manuelle
                            if hasattr(self, 'headless') and not self.headless:
                                self.logger.info("Mode visible activé. Veuillez résoudre manuellement le captcha dans le navigateur.")

                                # Attendre que l'utilisateur résolve le captcha (60 secondes max)
                                try:
                                    # Attendre que le captcha disparaisse ou que la page change
                                    # Utiliser une méthode plus robuste pour détecter la résolution du captcha
                                    self.logger.info("En attente de résolution manuelle du captcha (60 secondes max)...")

                                    # Vérifier si nous sommes sur la page de connexion
                                    current_url = page.url

                                    # Attendre que l'URL change (indiquant une redirection après connexion réussie)
                                    # ou que les éléments de captcha disparaissent
                                    # Enregistrer l'URL actuelle
                                    initial_url = current_url

                                    # Réduire le temps d'attente à 30 secondes pour éviter les blocages
                                    start_time = time.time()
                                    captcha_resolved = False
                                    max_wait_time = 30  # 30 secondes maximum

                                    while time.time() - start_time < max_wait_time and not captcha_resolved:
                                        try:
                                            # Vérifier si l'URL a changé
                                            current_url = page.url
                                            if current_url != initial_url:
                                                self.logger.info(f"URL changée de {initial_url} à {current_url}, captcha probablement résolu")
                                                captcha_resolved = True
                                                break

                                            # Vérifier si les iframes captcha ont disparu
                                            try:
                                                captcha_iframe = await page.query_selector("iframe[src*='captcha']")
                                                if captcha_iframe is None:
                                                    self.logger.info("Iframe captcha disparu, captcha probablement résolu")
                                                    captcha_resolved = True
                                                    break
                                            except Exception as e:
                                                self.logger.debug(f"Erreur lors de la vérification de l'iframe captcha: {e}")
                                                # Si une erreur se produit, continuer la vérification

                                            # Vérifier si le captcha a expiré
                                            refresh_button_selectors = [
                                                "button.refresh-button",
                                                "button.captcha-refresh",
                                                ".refresh-icon",
                                                "button:has-text('Réessayer')",
                                                "button:has-text('RÉESSAYER')",
                                                "img[alt='refresh']",
                                                ".captcha-refresh-icon"
                                            ]

                                            captcha_expired = False
                                            for selector in refresh_button_selectors:
                                                try:
                                                    if await page.is_visible(selector, timeout=1000):
                                                        captcha_expired = True
                                                        self.logger.info(f"Bouton de rafraîchissement du captcha détecté avec le sélecteur: {selector}")
                                                        # Cliquer sur le bouton de rafraîchissement
                                                        await page.click(selector)
                                                        self.logger.info("Clic sur le bouton de rafraîchissement du captcha")
                                                        await page.wait_for_timeout(2000)  # Attendre que le nouveau captcha se charge

                                                        # Capture d'écran après le rafraîchissement
                                                        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                                                        await page.screenshot(path=f"screenshots/captcha_after_refresh_manual_{timestamp}.png")
                                                        break
                                                except Exception as e:
                                                    self.logger.debug(f"Erreur lors de la vérification du bouton de rafraîchissement {selector}: {e}")

                                            # Si le captcha a expiré, vérifier si l'image du captcha contient une icône de rafraîchissement
                                            if not captcha_expired:
                                                captcha_image_selectors = [
                                                    "img[src*='captcha']",
                                                    "iframe[src*='captcha']",
                                                    ".captcha-image",
                                                    ".captcha-container img"
                                                ]

                                                for selector in captcha_image_selectors:
                                                    try:
                                                        if await page.is_visible(selector, timeout=1000):
                                                            # Vérifier si l'image contient une icône de rafraîchissement
                                                            element = await page.query_selector(selector)
                                                            if element:
                                                                box = await element.bounding_box()
                                                                if box:
                                                                    # Prendre une capture d'écran de l'élément
                                                                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                                                                    await page.screenshot(path=f"screenshots/captcha_element_manual_{timestamp}.png", clip=box)

                                                                    # Cliquer au centre de l'image du captcha pour le rafraîchir
                                                                    center_x = box["x"] + box["width"] / 2
                                                                    center_y = box["y"] + box["height"] / 2
                                                                    await page.mouse.click(center_x, center_y)
                                                                    self.logger.info("Clic au centre de l'image du captcha pour le rafraîchir")
                                                                    captcha_expired = True
                                                                    await page.wait_for_timeout(2000)  # Attendre que le nouveau captcha se charge

                                                                    # Capture d'écran après le rafraîchissement
                                                                    await page.screenshot(path=f"screenshots/captcha_after_center_click_manual_{timestamp}.png")
                                                                    break
                                                    except Exception as e:
                                                        self.logger.debug(f"Erreur lors de la vérification de l'image du captcha {selector}: {e}")

                                            # Attendre un peu avant de vérifier à nouveau
                                            await page.wait_for_timeout(1000)
                                        except Exception as e:
                                            # Si une erreur se produit (par exemple, page fermée), sortir de la boucle
                                            self.logger.warning(f"Erreur lors de la vérification de la résolution du captcha: {e}")
                                            break

                                    # Si le captcha a été résolu, attendre que la page se charge complètement
                                    if captcha_resolved:
                                        try:
                                            await page.wait_for_load_state("networkidle", timeout=10000)
                                            self.logger.info("Page chargée après résolution du captcha")
                                        except Exception as e:
                                            self.logger.warning(f"Erreur lors de l'attente du chargement de la page: {e}")
                                    else:
                                        self.logger.warning("Délai d'attente dépassé pour la résolution manuelle du captcha")

                                    self.logger.info("Captcha résolu manuellement avec succès!")
                                    captcha_solved = True
                                except Exception as e:
                                    self.logger.error(f"Délai d'attente dépassé pour la résolution manuelle: {e}")
                                    captcha_solved = False
                            else:
                                captcha_solved = False

                            if not captcha_solved:
                                # En mode simulation ou avec l'option continue_after_captcha_failure, on continue
                                if self.simulation_mode or hasattr(self, 'continue_after_captcha_failure') and self.continue_after_captcha_failure:
                                    self.logger.info("Mode simulation ou continue_after_captcha_failure activé: Continuation malgré l'échec du captcha")
                                    # Attendre un peu pour simuler une résolution
                                    await page.wait_for_timeout(2000)
                                    return True
                                else:
                                    self.logger.error("Échec de la connexion: captcha non résolu")
                                    return False
                    else:
                        # Captcha non slider (peut-être reCAPTCHA ou autre)
                        self.logger.warning("Type de captcha non pris en charge (non slider).")

                        # En mode visible, proposer une résolution manuelle
                        if hasattr(self, 'headless') and not self.headless:
                            self.logger.info("Mode visible activé. Veuillez résoudre manuellement le captcha dans le navigateur.")

                            # Attendre que l'utilisateur résolve le captcha (30 secondes max)
                            try:
                                await page.wait_for_function("""
                                    () => !document.querySelector('div:has-text("Verification Required")')
                                """, timeout=30000)

                                self.logger.info("Captcha résolu manuellement avec succès!")
                            except Exception as e:
                                self.logger.error(f"Délai d'attente dépassé pour la résolution manuelle: {e}")

                                # En mode simulation, on continue comme si le captcha était résolu
                                if self.simulation_mode:
                                    self.logger.info("Mode simulation: Simulation de la résolution du captcha")
                                    return True
                                else:
                                    self.logger.error("Échec de la connexion: captcha non résolu")
                                    return False
                        else:
                            # En mode headless, impossible de résoudre manuellement
                            if self.simulation_mode:
                                self.logger.info("Mode simulation: Simulation de la résolution du captcha")
                                return True
                            else:
                                self.logger.error("Échec de la connexion: captcha non résolu")
                                return False

                # Vérification du succès de la connexion
                success_selectors = [
                    ".user-profile",
                    ".dashboard",
                    ".logged-in",
                    "a:has-text('Déconnexion')",
                    "a:has-text('Mon compte')",
                    "div:has-text('Bienvenue')",
                    "div:has-text('Découvrir nos offres')",  # Basé sur votre capture d'écran
                    "a:has-text('Découvrir nos offres')",
                    "img[alt*='SeLoger']",
                    "img[src*='seloger']",
                    ".header-logo"
                ]

                # Vérifier si nous sommes sur une page d'erreur ou de déconnexion
                error_selectors = [
                    "div:has-text('Session expirée')",
                    "div:has-text('Vous avez été déconnecté')",
                    "div:has-text('Erreur d\\'authentification')",
                    "div:has-text('Erreur de connexion')"
                ]

                # Capture d'écran après tentative de connexion
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                await page.screenshot(path=f"screenshots/after_login_{timestamp}.png")

                # Attendre un peu plus longtemps pour s'assurer que la page est complètement chargée
                await page.wait_for_timeout(3000)

                # Vérifier l'URL actuelle
                current_url = page.url
                self.logger.info(f"URL actuelle après connexion: {current_url}")

                # Vérifier si nous sommes sur une page d'erreur
                is_error_page = False
                for selector in error_selectors:
                    try:
                        if await page.is_visible(selector, timeout=1000):
                            is_error_page = True
                            self.logger.warning(f"Page d'erreur détectée: {selector}")
                            break
                    except Exception:
                        continue

                if is_error_page:
                    self.logger.warning("Détection d'une page d'erreur après tentative de connexion")
                    # Essayer de se reconnecter une fois
                    if not hasattr(self, 'login_retry_count') or self.login_retry_count < 1:
                        self.login_retry_count = 1 if not hasattr(self, 'login_retry_count') else self.login_retry_count + 1
                        self.logger.info(f"Tentative de reconnexion ({self.login_retry_count}/1)...")

                        # Rafraîchir la page et réessayer
                        await page.reload()
                        await page.wait_for_load_state("networkidle", timeout=10000)

                        # Vérifier si nous sommes sur la page de connexion
                        if "login" not in page.url:
                            await page.goto("https://myselogerpro.com/login", timeout=30000)
                            await page.wait_for_load_state("networkidle", timeout=10000)

                        # Réessayer la connexion mais sans récursion pour éviter une boucle infinie
                        # Rafraîchir la page et réessayer manuellement les étapes de connexion
                        self.logger.info("Tentative de connexion manuelle après échec...")
                        await page.reload()
                        await page.wait_for_load_state("networkidle", timeout=10000)

                        # Remplir les champs de connexion manuellement
                        try:
                            # Remplir le champ email/username
                            for selector in email_selectors:
                                try:
                                    if await page.is_visible(selector, timeout=1000):
                                        await page.fill(selector, self.config["auth"]["username"])
                                        self.logger.info(f"Champ email rempli avec le sélecteur: {selector}")
                                        break
                                except Exception as e:
                                    self.logger.debug(f"Erreur lors du remplissage du champ email: {e}")
                                    continue

                            # Remplir le champ mot de passe
                            for selector in password_selectors:
                                try:
                                    if await page.is_visible(selector, timeout=1000):
                                        await page.fill(selector, self.config["auth"]["password"])
                                        self.logger.info(f"Champ mot de passe rempli avec le sélecteur: {selector}")
                                        break
                                except Exception as e:
                                    self.logger.debug(f"Erreur lors du remplissage du champ mot de passe: {e}")
                                    continue

                            # Cliquer sur le bouton de connexion
                            for selector in login_button_selectors:
                                try:
                                    if await page.is_visible(selector, timeout=1000):
                                        await page.click(selector)
                                        self.logger.info(f"Bouton de connexion cliqué avec le sélecteur: {selector}")
                                        break
                                except Exception as e:
                                    self.logger.debug(f"Erreur lors du clic sur le bouton de connexion: {e}")
                                    continue

                            # Attendre que la page se charge
                            await page.wait_for_load_state("networkidle", timeout=10000)

                            # Vérifier si nous sommes connectés
                            current_url = page.url
                            if "myselogerpro.com" in current_url and "login" not in current_url:
                                self.logger.info("Reconnexion manuelle réussie!")
                                return True
                            else:
                                self.logger.error("Échec de la reconnexion manuelle")
                                return False
                        except Exception as e:
                            self.logger.error(f"Erreur lors de la reconnexion manuelle: {e}")
                            return False
                    else:
                        self.logger.error("Échec après tentative de reconnexion")
                        return False

                # Vérifier si nous sommes sur la page d'accueil de SeLogerPro
                is_logged_in = False

                # Vérifier d'abord l'URL
                if "myselogerpro.com" in current_url and "login" not in current_url:
                    is_logged_in = True
                    self.logger.info(f"Connexion réussie! URL indiquant le succès: {current_url}")

                # Si l'URL ne confirme pas le succès, vérifier les éléments visuels
                if not is_logged_in:
                    for selector in success_selectors:
                        try:
                            if await page.is_visible(selector, timeout=1000):
                                is_logged_in = True
                                self.logger.info(f"Connexion réussie! Indicateur visuel trouvé: {selector}")
                                break
                        except Exception:
                            continue

                # Si toujours pas de succès, vérifier le contenu HTML
                if not is_logged_in:
                    try:
                        # Vérifier si le HTML contient des indices de connexion réussie
                        html_content = await page.content()
                        success_indicators = ["myselogerpro", "dashboard", "compte", "annonces", "déconnexion", "découvrir nos offres"]

                        for indicator in success_indicators:
                            if indicator.lower() in html_content.lower():
                                is_logged_in = True
                                self.logger.info(f"Connexion réussie! Indicateur trouvé dans le HTML: {indicator}")
                                break
                    except Exception as e:
                        self.logger.warning(f"Erreur lors de la vérification du HTML: {e}")

                # Réinitialiser le compteur de tentatives de connexion si la connexion a réussi
                if is_logged_in:
                    if hasattr(self, 'login_retry_count'):
                        delattr(self, 'login_retry_count')

                    # Réinitialiser également le compteur de boucles de redirection
                    if hasattr(self, 'last_redirect_url'):
                        delattr(self, 'last_redirect_url')

                if is_logged_in:
                    self.logger.info("Connexion réussie!")

                    # Sauvegarder l'état d'authentification pour les futures exécutions
                    try:
                        # Attendre un peu pour s'assurer que tous les cookies sont définis
                        await page.wait_for_timeout(2000)

                        # Obtenir le contexte du navigateur à partir de la page
                        browser_context = page.context

                        # Obtenir les cookies et le stockage local
                        cookies = await browser_context.cookies()

                        # Vérifier si les cookies contiennent des informations d'authentification
                        auth_cookies_found = False
                        for cookie in cookies:
                            if cookie.get('name', '').lower() in ['sessionid', 'auth', 'token', 'jwt', 'connect.sid']:
                                auth_cookies_found = True
                                self.logger.info(f"Cookie d'authentification trouvé: {cookie.get('name')}")
                                break

                        if not auth_cookies_found:
                            self.logger.warning("Aucun cookie d'authentification trouvé, la session pourrait être instable")

                        # Sauvegarder les cookies
                        storage_path = os.path.join(self.data_dir, "auth_state.json")
                        with open(storage_path, "w") as f:
                            json.dump({"cookies": cookies}, f)

                        # Essayer également de sauvegarder le stockage local si disponible
                        try:
                            local_storage = await page.evaluate("""() => {
                                const items = {};
                                for (let i = 0; i < localStorage.length; i++) {
                                    const key = localStorage.key(i);
                                    items[key] = localStorage.getItem(key);
                                }
                                return items;
                            }""")

                            # Sauvegarder le stockage local
                            storage_path_local = os.path.join(self.data_dir, "local_storage.json")
                            with open(storage_path_local, "w") as f:
                                json.dump(local_storage, f)
                            self.logger.info(f"Stockage local sauvegardé dans {storage_path_local}")
                        except Exception as ls_error:
                            self.logger.warning(f"Erreur lors de la sauvegarde du stockage local: {ls_error}")

                        self.logger.info(f"État d'authentification sauvegardé dans {storage_path}")
                    except Exception as e:
                        self.logger.warning(f"Erreur lors de la sauvegarde de l'état d'authentification: {e}")

                    # Navigation vers la page du portefeuille d'annonces
                    self.logger.info("Navigation vers la page du portefeuille d'annonces...")
                    try:
                        # Naviguer vers la page du portefeuille
                        portfolio_url = "https://myselogerpro.com/annonce/portefeuille"
                        self.logger.info(f"Accès à l'URL du portefeuille: {portfolio_url}")

                        # Capture d'écran avant navigation
                        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                        await page.screenshot(path=f"screenshots/before_portfolio_{timestamp}.png")

                        # Navigation vers la page du portefeuille
                        await page.goto(portfolio_url, timeout=30000)
                        await page.wait_for_load_state("networkidle", timeout=15000)

                        # Vérifier si nous avons été redirigés vers la page de connexion
                        current_url = page.url
                        if "login" in current_url:
                            self.logger.warning("Redirection vers la page de connexion détectée après navigation vers le portefeuille")

                            # Capture d'écran pour débogage
                            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                            await page.screenshot(path=f"screenshots/login_redirect_detected_{timestamp}.png")

                            # Essayer de se reconnecter sans appeler récursivement login()
                            self.logger.info("Tentative de reconnexion directe...")

                            # Remplir les champs de connexion manuellement
                            try:
                                # Vérifier si nous sommes sur la page de connexion
                                if "login" not in page.url:
                                    await page.goto("https://myselogerpro.com/login", timeout=30000)
                                    await page.wait_for_load_state("networkidle", timeout=10000)

                                # Définir les sélecteurs pour les champs de connexion
                                email_selectors = [
                                    "input[type='email']",
                                    "input[name='email']",
                                    "input[name='username']",
                                    "input[placeholder*='mail']",
                                    "input[placeholder*='identifiant']",
                                    "input[placeholder*='Identifiant']",
                                    "input.email",
                                    "input.username",
                                    "input#email",
                                    "input#username"
                                ]

                                password_selectors = [
                                    "input[type='password']",
                                    "input[name='password']",
                                    "input[placeholder*='mot de passe']",
                                    "input[placeholder*='Mot de passe']",
                                    "input.password",
                                    "input#password"
                                ]

                                login_button_selectors = [
                                    "button:has-text('Me connecter')",
                                    "button.btn-primary",
                                    "button.btn-connect",
                                    "button.login-button",
                                    "button[type='submit']",
                                    "input[type='submit']",
                                    ".btn-connect",
                                    ".btn-primary",
                                    "button.submit-button",
                                    "button:has-text('Connexion')",
                                    "button"
                                ]

                                # Remplir le champ email/username
                                email_filled = False
                                for selector in email_selectors:
                                    try:
                                        if await page.is_visible(selector, timeout=1000):
                                            await page.fill(selector, self.config["auth"]["username"])
                                            self.logger.info(f"Champ email rempli avec le sélecteur: {selector}")
                                            email_filled = True
                                            break
                                    except Exception as e:
                                        self.logger.debug(f"Erreur lors du remplissage du champ email: {e}")
                                        continue

                                if not email_filled:
                                    self.logger.warning("Impossible de trouver le champ email")

                                # Remplir le champ mot de passe
                                password_filled = False
                                for selector in password_selectors:
                                    try:
                                        if await page.is_visible(selector, timeout=1000):
                                            await page.fill(selector, self.config["auth"]["password"])
                                            self.logger.info(f"Champ mot de passe rempli avec le sélecteur: {selector}")
                                            password_filled = True
                                            break
                                    except Exception as e:
                                        self.logger.debug(f"Erreur lors du remplissage du champ mot de passe: {e}")
                                        continue

                                if not password_filled:
                                    self.logger.warning("Impossible de trouver le champ mot de passe")

                                # Cliquer sur le bouton de connexion
                                button_clicked = False
                                for selector in login_button_selectors:
                                    try:
                                        if await page.is_visible(selector, timeout=1000):
                                            await page.click(selector)
                                            self.logger.info(f"Bouton de connexion cliqué avec le sélecteur: {selector}")
                                            button_clicked = True
                                            break
                                    except Exception as e:
                                        self.logger.debug(f"Erreur lors du clic sur le bouton de connexion: {e}")
                                        continue

                                if not button_clicked:
                                    self.logger.warning("Impossible de trouver le bouton de connexion")
                                    login_success = False
                                else:
                                    # Attendre que la page se charge
                                    await page.wait_for_load_state("networkidle", timeout=10000)

                                    # Vérifier si nous sommes connectés
                                    current_url = page.url
                                    login_success = "myselogerpro.com" in current_url and "login" not in current_url

                                    if login_success:
                                        self.logger.info("Reconnexion directe réussie!")
                                    else:
                                        self.logger.error("Échec de la reconnexion directe")
                            except Exception as e:
                                self.logger.error(f"Erreur lors de la reconnexion directe: {e}")
                                login_success = False

                            if login_success:
                                # Réessayer la navigation vers le portefeuille
                                self.logger.info("Reconnexion réussie, nouvelle tentative de navigation vers le portefeuille")
                                await page.goto(portfolio_url, timeout=30000)
                                await page.wait_for_load_state("networkidle", timeout=15000)
                            else:
                                self.logger.error("Échec de la reconnexion")
                                return False

                        # Capture d'écran après navigation
                        await page.screenshot(path=f"screenshots/after_portfolio_{timestamp}.png")

                        # Cliquer sur l'onglet "SeLoger Bureaux & Commerces et SeLoger"
                        self.logger.info("Clic sur l'onglet 'SeLoger Bureaux & Commerces et SeLoger'...")

                        tab_selectors = [
                            "text='SeLoger Bureaux & Commerces et SeLoger'",
                            "a:has-text('SeLoger Bureaux & Commerces et SeLoger')",
                            "div:has-text('SeLoger Bureaux & Commerces et SeLoger')",
                            "li:has-text('SeLoger Bureaux & Commerces et SeLoger')",
                            "button:has-text('SeLoger Bureaux & Commerces et SeLoger')"
                        ]

                        tab_clicked = False
                        for selector in tab_selectors:
                            try:
                                if await page.is_visible(selector, timeout=2000):
                                    await page.click(selector)
                                    self.logger.info(f"Onglet trouvé et cliqué avec le sélecteur: {selector}")
                                    tab_clicked = True
                                    break
                            except Exception as e:
                                self.logger.debug(f"Erreur lors du clic sur l'onglet avec le sélecteur {selector}: {e}")

                        if not tab_clicked:
                            # Essayer avec JavaScript si les sélecteurs ne fonctionnent pas
                            self.logger.info("Tentative de clic sur l'onglet via JavaScript...")
                            await page.evaluate("""
                                () => {
                                    // Recherche plus approfondie pour trouver l'élément
                                    const searchTexts = [
                                        'SeLoger Bureaux & Commerces et SeLoger',
                                        'SeLoger Bureaux & Commerces',
                                        'Bureaux & Commerces et SeLoger',
                                        'Bureaux & Commerces',
                                        'Bureaux et Commerces'
                                    ];

                                    // Recherche dans tous les éléments interactifs
                                    const elements = Array.from(document.querySelectorAll('a, div, li, button, span, label, h1, h2, h3, h4, h5, h6'));

                                    // Essayer de trouver un élément contenant l'un des textes de recherche
                                    for (const searchText of searchTexts) {
                                        const matchingElements = elements.filter(el =>
                                            el.textContent.includes(searchText)
                                        );

                                        console.log(`Trouvé ${matchingElements.length} éléments contenant "${searchText}"`);

                                        for (const element of matchingElements) {
                                            console.log("Élément trouvé:", element.tagName, element.textContent.trim());

                                            // Essayer de cliquer sur l'élément
                                            try {
                                                element.click();
                                                console.log("Clic réussi sur l'élément");
                                                return true;
                                            } catch (e) {
                                                console.log("Erreur lors du clic sur l'élément:", e);

                                                // Essayer de trouver un parent cliquable
                                                let parent = element.parentElement;
                                                for (let i = 0; i < 5 && parent; i++) {  // Remonter jusqu'à 5 niveaux
                                                    try {
                                                        parent.click();
                                                        console.log("Clic réussi sur le parent niveau", i+1);
                                                        return true;
                                                    } catch (e2) {
                                                        console.log("Erreur lors du clic sur le parent niveau", i+1, ":", e2);
                                                        parent = parent.parentElement;
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    // Si aucun élément n'est trouvé, essayer de cliquer sur un onglet ou un lien
                                    const tabElements = Array.from(document.querySelectorAll('.tab, .nav-item, .nav-link, [role="tab"]'));
                                    for (const tab of tabElements) {
                                        if (tab.textContent.includes('SeLoger') || tab.textContent.includes('Bureaux')) {
                                            try {
                                                tab.click();
                                                console.log("Clic réussi sur l'onglet:", tab.textContent);
                                                return true;
                                            } catch (e) {
                                                console.log("Erreur lors du clic sur l'onglet:", e);
                                            }
                                        }
                                    }

                                    return false;
                                }
                            """)

                        # Attendre que la page se charge après le clic
                        await page.wait_for_load_state("networkidle", timeout=10000)

                        # Capture d'écran après le clic sur l'onglet
                        await page.screenshot(path=f"screenshots/after_tab_click_{timestamp}.png")

                        # Cliquer sur le tri par date
                        self.logger.info("Clic sur le tri par date...")

                        date_selectors = [
                            "text='Date'",
                            "a:has-text('Date')",
                            "div:has-text('Date')",
                            "th:has-text('Date')",
                            "button:has-text('Date')",
                            "span:has-text('Date')"
                        ]

                        date_clicked = False
                        for selector in date_selectors:
                            try:
                                if await page.is_visible(selector, timeout=2000):
                                    await page.click(selector)
                                    self.logger.info(f"Tri par date trouvé et cliqué avec le sélecteur: {selector}")
                                    date_clicked = True
                                    break
                            except Exception as e:
                                self.logger.debug(f"Erreur lors du clic sur le tri par date avec le sélecteur {selector}: {e}")

                        if not date_clicked:
                            # Essayer avec JavaScript si les sélecteurs ne fonctionnent pas
                            self.logger.info("Tentative de clic sur le tri par date via JavaScript...")
                            await page.evaluate("""
                                () => {
                                    console.log("Recherche du tri par date via JavaScript...");

                                    // Recherche plus approfondie pour trouver l'élément de tri par date
                                    const dateElements = Array.from(document.querySelectorAll('a, div, th, button, span, label, td, tr, th'))
                                        .filter(el => {
                                            const text = el.textContent.trim();
                                            return text === 'Date' || text === 'date' || text === 'DATE' ||
                                                   text.includes('Date de') || text.includes('date de') ||
                                                   text.includes('Trier par date');
                                        });

                                    console.log(`Trouvé ${dateElements.length} éléments de date potentiels`);

                                    // Essayer de cliquer sur chaque élément trouvé
                                    for (const element of dateElements) {
                                        console.log("Élément date trouvé:", element.tagName, element.textContent.trim());

                                        // Vérifier si l'élément a des attributs de tri
                                        const hasSortAttributes = element.hasAttribute('data-sort') ||
                                                                 element.hasAttribute('sort') ||
                                                                 element.classList.contains('sort') ||
                                                                 element.classList.contains('sortable') ||
                                                                 element.getAttribute('role') === 'button';

                                        if (hasSortAttributes) {
                                            console.log("L'élément a des attributs de tri");
                                        }

                                        // Essayer de cliquer sur l'élément
                                        try {
                                            element.click();
                                            console.log("Clic réussi sur l'élément de date");
                                            return true;
                                        } catch (e) {
                                            console.log("Erreur lors du clic sur l'élément de date:", e);

                                            // Essayer de trouver un parent cliquable
                                            let parent = element.parentElement;
                                            for (let i = 0; i < 3 && parent; i++) {  // Remonter jusqu'à 3 niveaux
                                                try {
                                                    parent.click();
                                                    console.log("Clic réussi sur le parent niveau", i+1);
                                                    return true;
                                                } catch (e2) {
                                                    console.log("Erreur lors du clic sur le parent niveau", i+1, ":", e2);
                                                    parent = parent.parentElement;
                                                }
                                            }
                                        }
                                    }

                                    // Si aucun élément n'est trouvé, chercher des éléments de tri génériques
                                    const sortElements = Array.from(document.querySelectorAll(
                                        '[data-sort], .sort, .sortable, th[role="columnheader"], [aria-sort]'
                                    ));

                                    console.log(`Trouvé ${sortElements.length} éléments de tri génériques`);

                                    // Chercher un élément de tri qui pourrait être lié à la date
                                    for (const element of sortElements) {
                                        const text = element.textContent.trim();
                                        console.log("Élément de tri trouvé:", element.tagName, text);

                                        if (text.includes('Date') || text.includes('date') ||
                                            text.includes('Créé') || text.includes('créé') ||
                                            text.includes('Modifié') || text.includes('modifié')) {
                                            try {
                                                element.click();
                                                console.log("Clic réussi sur l'élément de tri");
                                                return true;
                                            } catch (e) {
                                                console.log("Erreur lors du clic sur l'élément de tri:", e);
                                            }
                                        }
                                    }

                                    // Si toujours rien, essayer de cliquer sur n'importe quel élément de tri
                                    if (sortElements.length > 0) {
                                        try {
                                            sortElements[0].click();
                                            console.log("Clic réussi sur le premier élément de tri disponible");
                                            return true;
                                        } catch (e) {
                                            console.log("Erreur lors du clic sur le premier élément de tri:", e);
                                        }
                                    }

                                    return false;
                                }
                            """)

                        # Attendre que le menu de tri apparaisse
                        await page.wait_for_timeout(1000)

                        # Capture d'écran après le clic sur le tri par date
                        await page.screenshot(path=f"screenshots/after_date_click_{timestamp}.png")

                        # Cliquer sur "Le + ancien d'abord"
                        self.logger.info("Sélection de 'Le + ancien d'abord'...")

                        oldest_first_selectors = [
                            "text='Le + ancien d'abord'",
                            "a:has-text('Le + ancien')",
                            "div:has-text('Le + ancien')",
                            "li:has-text('Le + ancien')",
                            "button:has-text('Le + ancien')",
                            "span:has-text('Le + ancien')",
                            "a:has-text('ancien d'abord')",
                            "div:has-text('ancien d'abord')",
                            "li:has-text('ancien d'abord')",
                            "button:has-text('ancien d'abord')",
                            "span:has-text('ancien d'abord')"
                        ]

                        oldest_clicked = False
                        for selector in oldest_first_selectors:
                            try:
                                if await page.is_visible(selector, timeout=2000):
                                    await page.click(selector)
                                    self.logger.info(f"Option 'Le + ancien d'abord' trouvée et cliquée avec le sélecteur: {selector}")
                                    oldest_clicked = True
                                    break
                            except Exception as e:
                                self.logger.debug(f"Erreur lors du clic sur 'Le + ancien d'abord' avec le sélecteur {selector}: {e}")

                        if not oldest_clicked:
                            # Essayer avec JavaScript si les sélecteurs ne fonctionnent pas
                            self.logger.info("Tentative de clic sur 'Le + ancien d'abord' via JavaScript...")
                            await page.evaluate("""
                                () => {
                                    // Recherche plus approfondie pour trouver l'élément
                                    const searchTexts = ["Le + ancien d'abord", "Le + ancien", "ancien d'abord", "ancien"];

                                    // Recherche dans tous les éléments interactifs
                                    const elements = Array.from(document.querySelectorAll('a, div, li, button, span, option, label'));

                                    // Essayer de trouver un élément contenant l'un des textes de recherche
                                    for (const searchText of searchTexts) {
                                        const element = elements.find(el =>
                                            el.textContent.toLowerCase().includes(searchText.toLowerCase())
                                        );

                                        if (element) {
                                            console.log("Élément trouvé avec le texte:", element.textContent);

                                            // Vérifier si l'élément est dans un menu déroulant
                                            if (element.tagName === 'OPTION') {
                                                // Si c'est une option, sélectionner sa valeur dans le select parent
                                                const select = element.closest('select');
                                                if (select) {
                                                    select.value = element.value;
                                                    select.dispatchEvent(new Event('change', { bubbles: true }));
                                                    return true;
                                                }
                                            }

                                            // Essayer de cliquer sur l'élément
                                            element.click();

                                            // Si l'élément est dans un menu déroulant ou une liste
                                            const parentMenu = element.closest('ul, ol, div[role="menu"], div[role="listbox"]');
                                            if (parentMenu) {
                                                // Simuler un clic sur le parent aussi au cas où
                                                parentMenu.click();
                                            }

                                            return true;
                                        }
                                    }

                                    // Si aucun élément n'est trouvé, essayer de cliquer sur un élément de tri
                                    const sortElements = Array.from(document.querySelectorAll('[data-sort="asc"], [data-sort="oldest"], [data-sort="date-asc"]'));
                                    if (sortElements.length > 0) {
                                        sortElements[0].click();
                                        return true;
                                    }

                                    return false;
                                }
                            """)

                        # Attendre que la page se charge après le tri
                        await page.wait_for_load_state("networkidle", timeout=10000)

                        # Capture d'écran finale après le tri
                        await page.screenshot(path=f"screenshots/final_sorted_view_{timestamp}.png")

                        self.logger.info("Navigation et tri terminés avec succès!")

                    except Exception as e:
                        self.logger.error(f"Erreur lors de la navigation vers le portefeuille: {e}")

                    return True
                else:
                    self.logger.error("Échec de la connexion. Vérifiez vos identifiants.")
                    return False

            except Exception as e:
                self.logger.error(f"Erreur lors de la connexion: {e}")
                # Capture d'écran en cas d'erreur
                if self.config["error_handling"]["error_logs"]["screenshot_on_error"]:
                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    try:
                        await page.screenshot(path=f"screenshots/login_error_{timestamp}.png")
                    except Exception:
                        pass
                return False

    async def find_expired_properties(self, page: Page) -> List[str]:
        """Trouve les propriétés expirées qui nécessitent un renouvellement"""
        with tracer.start_as_current_span("find_expired"):
            self.logger.info("Recherche des propriétés expirées...")

            # Capture d'écran pour débogage
            if self.config["error_handling"]["error_logs"]["screenshot_on_error"]:
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                await page.screenshot(path=f"screenshots/find_expired_{timestamp}.png")

            # Stratégie 1: Recherche des annonces avec statut "Expirée" ou "À renouveler"
            property_ids = []

            # Sélecteurs pour les annonces expirées
            expired_selectors = [
                ".property-card.expired",
                ".property-card[data-status='expired']",
                ".property-item.expired",
                ".property-row.expired",
                "tr.expired",
                "div[class*='expired']",
                "div[data-status='expired']",
                "tr:has-text('Expirée')",
                "div:has-text('Expirée'):not(:has(div))",
                "span:has-text('Expirée')",
                "div:has-text('À renouveler')",
                "div:has-text('A renouveler')",
                "div:has-text('Renouvelez')"
            ]

            # Essayer chaque sélecteur
            for selector in expired_selectors:
                try:
                    self.logger.info(f"Recherche avec le sélecteur: {selector}")
                    property_elements = await page.query_selector_all(selector)

                    if property_elements:
                        self.logger.info(f"Trouvé {len(property_elements)} éléments avec le sélecteur {selector}")

                        # Extraction des IDs
                        for element in property_elements:
                            # Essayer différents attributs pour l'ID
                            for attr in ["data-property-id", "data-id", "id", "data-annonce-id"]:
                                prop_id = await element.get_attribute(attr)
                                if prop_id:
                                    property_ids.append(prop_id)
                                    break

                            # Si aucun ID trouvé, essayer de trouver un lien vers la propriété
                            if not prop_id:
                                link_element = await element.query_selector("a[href*='/annonce/'], a[href*='/annonces/']")
                                if link_element:
                                    href = await link_element.get_attribute("href")
                                    if href:
                                        # Extraire l'ID de l'URL
                                        import re
                                        match = re.search(r'/annonces?/(\d+)', href)
                                        if match:
                                            property_ids.append(match.group(1))

                        # Si des propriétés ont été trouvées, arrêter la recherche
                        if property_ids:
                            break
                except Exception as e:
                    self.logger.warning(f"Erreur lors de la recherche avec le sélecteur {selector}: {e}")

            # Si aucun élément trouvé, utiliser l'analyse visuelle (stratégie 2)
            if not property_ids:
                self.logger.info("Aucune propriété expirée trouvée via DOM, utilisation de l'analyse visuelle")

                # Capture d'écran pour l'analyse visuelle
                screenshot_path = f"screenshots/for_vision_analysis_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                await page.screenshot(path=screenshot_path)
                self.logger.info(f"Capture d'écran pour analyse visuelle sauvegardée: {screenshot_path}")

                # Utiliser l'analyse visuelle
                expired_via_vision = await self.vision_model.detect_expired_properties(page)
                vision_ids = [prop["id"] for prop in expired_via_vision]

                if vision_ids:
                    self.logger.info(f"Trouvé {len(vision_ids)} propriétés expirées via analyse visuelle")
                    property_ids.extend(vision_ids)
                else:
                    self.logger.warning("Aucune propriété expirée trouvée via analyse visuelle")

                    # Stratégie 3: Rechercher toutes les propriétés et filtrer par âge
                    self.logger.info("Recherche de toutes les propriétés pour filtrage par âge")

                    # Sélecteurs pour toutes les propriétés
                    all_property_selectors = [
                        ".property-card",
                        ".property-item",
                        ".property-row",
                        "tr.property",
                        "div[class*='property']",
                        "div[data-type='property']"
                    ]

                    for selector in all_property_selectors:
                        try:
                            all_elements = await page.query_selector_all(selector)
                            if all_elements:
                                self.logger.info(f"Trouvé {len(all_elements)} propriétés au total")

                                # Filtrer par date (si disponible)
                                for element in all_elements:
                                    # Chercher la date de publication/mise à jour
                                    date_text = await element.evaluate("""el => {
                                        const dateEl = el.querySelector('[data-date], [class*="date"], span:contains("publié"), span:contains("mis à jour")');
                                        return dateEl ? dateEl.textContent : '';
                                    }""")

                                    if date_text:
                                        # Analyser la date pour déterminer l'âge
                                        # Logique simplifiée: si le texte contient "il y a X jours" et X >= threshold
                                        import re
                                        match = re.search(r'il y a (\d+) jours?', date_text)
                                        if match:
                                            days = int(match.group(1))
                                            threshold = self.config["renewal"]["listing_age_threshold_days"]

                                            if days >= threshold:
                                                # Extraire l'ID comme précédemment
                                                for attr in ["data-property-id", "data-id", "id", "data-annonce-id"]:
                                                    prop_id = await element.get_attribute(attr)
                                                    if prop_id:
                                                        property_ids.append(prop_id)
                                                        break

                                # Si des propriétés ont été trouvées, arrêter la recherche
                                if property_ids:
                                    break
                        except Exception as e:
                            self.logger.warning(f"Erreur lors de la recherche avec le sélecteur {selector}: {e}")

            # Dédupliquer les IDs
            property_ids = list(set(property_ids))

            # En mode simulation, si aucune propriété trouvée, ajouter des propriétés simulées
            if not property_ids and self.simulation_mode:
                self.logger.info("Mode simulation: Ajout de propriétés simulées")
                property_ids = ["sim_property_1", "sim_property_2", "sim_property_3"]

            self.logger.info(f"Trouvé {len(property_ids)} propriétés expirées au total")
            return property_ids

    async def renew_property(self, page: Page, property_id: str) -> bool:
        """Renouvelle une propriété spécifique"""
        with tracer.start_as_current_span(f"renew_property_{property_id}"):
            try:
                self.logger.info(f"Tentative de renouvellement de la propriété {property_id}")

                # Capture d'écran avant renouvellement
                if self.config["error_handling"]["error_logs"]["screenshot_on_error"]:
                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    await page.screenshot(path=f"screenshots/before_renewal_{property_id}_{timestamp}.png")

                # Navigation vers la page de la propriété
                property_url = f"https://myselogerpro.com/annonces/{property_id}"
                self.logger.info(f"Navigation vers la page de la propriété: {property_url}")

                try:
                    await page.goto(property_url, timeout=30000)
                    await page.wait_for_load_state("networkidle", timeout=10000)
                except Exception as nav_error:
                    self.logger.error(f"Erreur lors de la navigation vers la propriété: {nav_error}")
                    # Gérer les erreurs de proxy
                    await self._handle_proxy_error(nav_error)

                    # Essayer une URL alternative
                    alt_url = f"https://myselogerpro.com/annonce/{property_id}"
                    self.logger.info(f"Tentative avec URL alternative: {alt_url}")
                    await page.goto(alt_url, timeout=30000)
                    await page.wait_for_load_state("networkidle", timeout=10000)

                # Stratégie 1: Recherche de boutons de renouvellement par texte
                renew_button_selectors = [
                    "button:has-text('Renouveler')",
                    "a:has-text('Renouveler')",
                    "button:has-text('Republier')",
                    "a:has-text('Republier')",
                    "button.renew-button",
                    "[data-action='renew']",
                    "[aria-label*='renouveler']",
                    "[aria-label*='republier']"
                ]

                for selector in renew_button_selectors:
                    if await page.is_visible(selector, timeout=1000):
                        self.logger.info(f"Bouton de renouvellement trouvé: {selector}")
                        await page.click(selector)
                        await page.wait_for_load_state("networkidle", timeout=10000)

                        # Vérifier s'il y a une boîte de dialogue de confirmation
                        confirm_selectors = [
                            "button:has-text('Confirmer')",
                            "button:has-text('OK')",
                            "button:has-text('Oui')",
                            "button[type='submit']"
                        ]

                        for confirm_selector in confirm_selectors:
                            if await page.is_visible(confirm_selector, timeout=2000):
                                self.logger.info(f"Boîte de dialogue de confirmation trouvée, confirmation via {confirm_selector}")
                                await page.click(confirm_selector)
                                await page.wait_for_load_state("networkidle", timeout=10000)
                                break

                        self.logger.info(f"Propriété {property_id} renouvelée avec succès (stratégie 1)")
                        return True

                # Stratégie 2: Création d'une nouvelle annonce à partir du modèle
                if self.config["renewal"]["strategy"] == "create_new_from_template":
                    self.logger.info("Tentative de création d'une nouvelle annonce à partir du modèle")

                    # Recherche de boutons pour créer une nouvelle annonce
                    create_button_selectors = [
                        "button:has-text('Dupliquer')",
                        "a:has-text('Dupliquer')",
                        "button:has-text('Copier')",
                        "a:has-text('Copier')",
                        "[data-action='duplicate']",
                        "[aria-label*='dupliquer']",
                        "[aria-label*='copier']"
                    ]

                    for selector in create_button_selectors:
                        if await page.is_visible(selector, timeout=1000):
                            self.logger.info(f"Bouton de duplication trouvé: {selector}")
                            await page.click(selector)
                            await page.wait_for_load_state("networkidle", timeout=10000)

                            # Recherche de boutons pour publier la nouvelle annonce
                            publish_button_selectors = [
                                "button:has-text('Publier')",
                                "button[type='submit']",
                                "button.publish-button",
                                "[data-action='publish']"
                            ]

                            for publish_selector in publish_button_selectors:
                                if await page.is_visible(publish_selector, timeout=2000):
                                    self.logger.info(f"Bouton de publication trouvé: {publish_selector}")
                                    await page.click(publish_selector)
                                    await page.wait_for_load_state("networkidle", timeout=10000)
                                    break

                            self.logger.info(f"Propriété {property_id} renouvelée via création (stratégie 2)")
                            return True

                # Stratégie 3: Utilisation de l'analyse visuelle pour trouver le bouton
                self.logger.info("Tentative de renouvellement via analyse visuelle")
                vision_elements = await self.vision_model.analyze_screenshot(page)
                renew_buttons = [el for el in vision_elements if el["type"] == "button" and "Renouveler" in el["text"]]

                if renew_buttons:
                    # Clic sur les coordonnées du bouton détecté
                    button = renew_buttons[0]
                    x = button["bbox"][0] + (button["bbox"][2] - button["bbox"][0]) / 2
                    y = button["bbox"][1] + (button["bbox"][3] - button["bbox"][1]) / 2
                    self.logger.info(f"Bouton de renouvellement détecté visuellement aux coordonnées ({x}, {y})")
                    await page.mouse.click(x, y)
                    await page.wait_for_load_state("networkidle", timeout=10000)
                    self.logger.info(f"Propriété {property_id} renouvelée avec succès (stratégie 3)")
                    return True

                # Capture d'écran en cas d'échec
                if self.config["error_handling"]["error_logs"]["screenshot_on_error"]:
                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    await page.screenshot(path=f"screenshots/renewal_failed_{property_id}_{timestamp}.png")

                self.logger.error(f"Impossible de renouveler la propriété {property_id}")
                return False

            except Exception as e:
                self.logger.error(f"Erreur lors du renouvellement de la propriété {property_id}: {e}")

                # Capture d'écran en cas d'erreur
                if self.config["error_handling"]["error_logs"]["screenshot_on_error"]:
                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    try:
                        await page.screenshot(path=f"screenshots/renewal_error_{property_id}_{timestamp}.png")
                    except Exception:
                        pass

                return False

    async def check_existing_session(self, page: Page) -> bool:
        """Vérifie si une session existante est valide"""
        try:
            self.logger.info("Vérification de la session existante...")

            # Aller directement à la page du portefeuille
            await page.goto("https://myselogerpro.com/annonce/portefeuille", timeout=30000)
            await page.wait_for_load_state("domcontentloaded", timeout=15000)

            # Vérifier si nous sommes redirigés vers la page de login
            current_url = page.url
            if "login" in current_url.lower():
                self.logger.info("Session expirée - redirection vers login détectée")
                return False

            # Vérifier si nous sommes sur la bonne page
            if "portefeuille" in current_url.lower():
                self.logger.info("Session valide - accès direct au portefeuille réussi")
                return True

            # Vérifier le contenu de la page pour s'assurer qu'on est connecté
            try:
                # Chercher des éléments qui indiquent qu'on est connecté
                is_logged_in = await page.evaluate("""() => {
                    // Chercher des indicateurs de connexion
                    const indicators = [
                        'portefeuille',
                        'mes annonces',
                        'tableau de bord',
                        'déconnexion',
                        'mon compte'
                    ];

                    const pageText = document.body.innerText.toLowerCase();
                    return indicators.some(indicator => pageText.includes(indicator));
                }""")

                if is_logged_in:
                    self.logger.info("Session valide confirmée par le contenu de la page")
                    return True
                else:
                    self.logger.info("Session invalide - contenu de page non reconnu")
                    return False

            except Exception as e:
                self.logger.warning(f"Erreur lors de la vérification du contenu: {e}")
                return False

        except Exception as e:
            self.logger.warning(f"Erreur lors de la vérification de session: {e}")
            return False

    async def execute_workflow(self) -> Dict[str, Any]:
        """Exécute le workflow complet de renouvellement avec gestion intelligente de session"""
        start_time = time.time()
        memory_start = self._get_memory_usage()

        results = {
            "start_time": datetime.datetime.now().isoformat(),
            "renewed": [],
            "failed": [],
            "errors": [],
            "performance_metrics": {},
            "blocked": False,
            "proxy_info": {},
            "session_reused": False
        }

        # Pause initiale variable pour éviter la prévisibilité
        initial_delay = random.randint(1, 15)
        self.logger.info(f"Pause initiale de {initial_delay} secondes pour variabilité...")
        await asyncio.sleep(initial_delay)

        # Vérifier l'âge du fichier auth_state.json
        auth_file_path = os.path.join("data", "auth_state.json")
        should_force_new_proxy = True

        if os.path.exists(auth_file_path):
            # Vérifier l'âge du fichier (ne pas forcer nouveau proxy si récent)
            file_age = time.time() - os.path.getmtime(auth_file_path)
            if file_age < 3600:  # Moins d'1 heure
                should_force_new_proxy = False
                self.logger.info(f"Fichier auth_state.json récent ({file_age/60:.1f} min), réutilisation possible")

        # Initialiser et tester le proxy si configuré
        if self.proxy_rotator:
            self.logger.info("Initialisation du proxy IProyal...")
            try:
                # Forcer une nouvelle session IP seulement si nécessaire
                if should_force_new_proxy and self.proxy_rotator.session_rotation_per_run:
                    self.logger.info("Nouvelle session proxy forcée")
                    await self.proxy_rotator.force_rotation()
                elif not should_force_new_proxy:
                    self.logger.info("Réutilisation de la session proxy existante")

                # Obtenir la configuration proxy actuelle
                proxy_config = await self.proxy_rotator.get_current_proxy()
                if proxy_config:
                    # Tester la connectivité du proxy
                    self.logger.info("Test de connectivité du proxy...")
                    if await self.proxy_rotator.test_current_proxy_connectivity():
                        proxy_info = self.proxy_rotator.get_current_proxy_info()
                        results["proxy_info"] = proxy_info
                        self.logger.info(f"Proxy actif: {proxy_info['country']} (Session: {proxy_info['session_id']})")
                    else:
                        self.logger.warning("Échec du test de connectivité proxy, tentative de rotation...")
                        await self.proxy_rotator.force_rotation()
                        proxy_config = await self.proxy_rotator.get_current_proxy()
                        if proxy_config:
                            proxy_info = self.proxy_rotator.get_current_proxy_info()
                            results["proxy_info"] = proxy_info
                else:
                    self.logger.warning("Aucun proxy disponible, exécution sans proxy")
                    results["proxy_info"] = {"status": "no_proxy_available"}
            except Exception as proxy_error:
                self.logger.error(f"Erreur lors de l'initialisation du proxy: {proxy_error}")
                results["errors"].append(f"Erreur proxy: {str(proxy_error)}")
                results["proxy_info"] = {"status": "proxy_error", "error": str(proxy_error)}

        # Création du répertoire pour les captures d'écran si nécessaire
        screenshots_dir = "screenshots"
        if not os.path.exists(screenshots_dir):
            os.makedirs(screenshots_dir)

        # Configuration du circuit breaker pour les tentatives
        # Le circuit breaker est déjà configuré dans __init__ avec les paramètres de retry_policy

        try:
            if self.simulation_mode:
                    # En mode simulation, on simule toutes les étapes sans lancer Playwright
                self.logger.info("Simulation: Initialisation du navigateur")
                browser_launch_time = 1.2  # Temps simulé
                auth_time = 0.5  # Temps simulé
                navigation_time = 2.3  # Temps simulé

                # Simulation des propriétés expirées
                expired_properties = ["sim_property_1", "sim_property_2", "sim_property_3"]
                search_time = 1.1  # Temps simulé

                # Simulation du renouvellement
                renewal_times = []
                for prop_id in expired_properties:
                    self.logger.info(f"Simulation: Renouvellement de la propriété {prop_id}")
                    # Utiliser un délai fixe court en mode simulation pour éviter les blocages
                    delay = 0.5  # Délai fixe de 0.5 seconde
                    await asyncio.sleep(delay)

                    # Simuler un succès pour la plupart des propriétés
                    if random.random() > 0.2:  # 80% de chance de succès
                        results["renewed"].append(prop_id)
                        self.logger.info(f"Simulation: Propriété {prop_id} renouvelée avec succès")
                    else:
                        results["failed"].append(prop_id)
                        results["errors"].append(f"Erreur simulée pour {prop_id}")
                        self.logger.error(f"Simulation: Échec du renouvellement de la propriété {prop_id}")

                    renewal_time = random.uniform(1.0, 3.0)
                    renewal_times.append(renewal_time)

                total_renewal_time = sum(renewal_times)
                avg_renewal_time = sum(renewal_times) / len(renewal_times) if renewal_times else 0

            else:
                # Mode normal avec Playwright
                async with async_playwright() as p:
                    browser_launch_start = time.time()

                    # Déterminer si on doit lancer en mode headless ou non
                    headless = True
                    if hasattr(self, 'headless') and self.headless is False:
                        headless = False
                        self.logger.info("Lancement du navigateur en mode visible (non-headless)")

                    # Utiliser notre nouvelle méthode _launch_browser pour une meilleure protection contre la détection de bot
                    browser, context = await self._launch_browser(headless=headless)

                    browser_launch_time = time.time() - browser_launch_start

                    try:
                        # Chargement de l'état d'authentification
                        auth_start = time.time()
                        is_auth_loaded = await self.load_authentication_state(
                            context,
                            self.config["auth"]["password"]
                        )
                        auth_time = time.time() - auth_start

                        if is_auth_loaded:
                            self.logger.info("État d'authentification chargé avec succès")
                        else:
                            self.logger.info("Aucun état d'authentification trouvé ou état invalide")

                        page = await context.new_page()

                        # Vérification de session existante si l'état d'auth a été chargé
                        session_valid = False
                        if is_auth_loaded:
                            self.logger.info("Vérification de la validité de la session existante...")
                            session_valid = await self.check_existing_session(page)
                            results["session_reused"] = session_valid

                            if session_valid:
                                self.logger.info("Session existante valide - pas besoin de se reconnecter")
                                # Aller directement au renouvellement
                                navigation_time = 0
                                login_time = 0
                                is_logged_in = True
                            else:
                                self.logger.info("Session existante invalide - nouvelle connexion nécessaire")
                                # Supprimer le fichier auth_state.json invalide
                                try:
                                    auth_file_path = os.path.join("data", "auth_state.json")
                                    if os.path.exists(auth_file_path):
                                        os.remove(auth_file_path)
                                        self.logger.info("Fichier auth_state.json invalide supprimé")
                                except Exception as e:
                                    self.logger.warning(f"Erreur lors de la suppression du fichier auth: {e}")

                        # Navigation vers le site seulement si session invalide
                        if not session_valid:
                            navigation_start = time.time()
                            try:
                                self.logger.info("Tentative de connexion à SeLogerPro...")

                                # Utiliser l'URL personnalisée si définie, sinon essayer plusieurs URLs
                                if hasattr(self, 'custom_url') and self.custom_url:
                                    login_url = self.custom_url
                                else:
                                    # Essayer d'abord l'URL que vous avez fournie dans la capture d'écran
                                    login_url = "https://myselogerpro.com/login"

                                self.logger.info(f"Accès à l'URL de connexion: {login_url}")

                            # Capture d'écran avant navigation
                            if self.config["error_handling"]["error_logs"]["screenshot_on_error"]:
                                await page.screenshot(path=f"screenshots/before_navigation.png")

                            # Navigation avec un timeout plus long et une meilleure gestion des erreurs
                            self.logger.info(f"Navigation vers {login_url} avec un timeout de 45 secondes...")
                            try:
                                await page.goto(login_url, timeout=45000, wait_until="domcontentloaded")
                                self.logger.info("Page chargée (domcontentloaded)")

                                # Attendre un peu plus pour que les scripts se chargent
                                await page.wait_for_timeout(2000)

                                # Vérifier si la page est bloquée
                                if await self.check_for_blocking(page):
                                    self.logger.error("BLOCAGE DÉTECTÉ lors du chargement initial")
                                    # Capture d'écran pour débogage
                                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                                    await page.screenshot(path=f"screenshots/blocked_initial_{timestamp}.png")

                                # Capture d'écran pour débogage
                                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                                await page.screenshot(path=f"screenshots/page_loaded_initial_{timestamp}.png")

                                # Attendre que la page soit interactive
                                self.logger.info("Attente du chargement complet de la page...")
                                try:
                                    await page.wait_for_load_state("networkidle", timeout=10000)
                                    self.logger.info("Réseau inactif (networkidle)")
                                except Exception as net_e:
                                    self.logger.warning(f"Timeout sur networkidle, mais la page est chargée: {net_e}")
                                    # Continuer même si networkidle n'est pas atteint
                            except Exception as goto_e:
                                self.logger.error(f"Erreur lors de la navigation vers {login_url}: {goto_e}")
                                # Gérer les erreurs de proxy
                                await self._handle_proxy_error(goto_e)
                                # Capture d'écran pour débogage
                                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                                try:
                                    await page.screenshot(path=f"screenshots/navigation_error_{timestamp}.png")
                                except Exception:
                                    self.logger.warning("Impossible de prendre une capture d'écran après erreur de navigation")
                                raise goto_e

                            # Capture d'écran après navigation
                            if self.config["error_handling"]["error_logs"]["screenshot_on_error"]:
                                await page.screenshot(path=f"screenshots/after_navigation.png")

                            # Vérifier si nous sommes sur la page de connexion
                            if "login" not in page.url and not await page.is_visible("input[type='password']", timeout=2000):
                                self.logger.info("Redirection vers la page de connexion...")

                                # Capture d'écran avant redirection
                                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                                await page.screenshot(path=f"screenshots/before_redirect_{timestamp}.png")

                                # Vérifier si nous sommes sur une page de déconnexion ou d'erreur
                                current_url = page.url
                                self.logger.info(f"URL actuelle avant redirection: {current_url}")

                                # Vérifier si nous sommes dans une boucle de redirection
                                if hasattr(self, 'last_redirect_url') and self.last_redirect_url == current_url:
                                    self.logger.warning("Détection d'une boucle de redirection! Nettoyage complet de la session...")

                                    # Vider complètement le cache et les cookies
                                    await context.clear_cookies()

                                    # Fermer et rouvrir une nouvelle page
                                    await page.close()
                                    page = await context.new_page()

                                    # Attendre un peu avant de réessayer
                                    await page.wait_for_timeout(3000)
                                else:
                                    # Enregistrer l'URL actuelle pour détecter les boucles
                                    self.last_redirect_url = current_url

                                # Naviguer vers la page de connexion
                                self.logger.info("Navigation vers la page de connexion...")
                                await page.goto("https://myselogerpro.com/login", timeout=30000)
                                await page.wait_for_load_state("networkidle", timeout=10000)

                                # Capture d'écran après redirection
                                await page.screenshot(path=f"screenshots/after_redirect_{timestamp}.png")

                                # Vérifier si nous sommes bien sur la page de connexion
                                if "login" not in page.url:
                                    self.logger.warning(f"Redirection inattendue vers {page.url} au lieu de la page de connexion")

                                    # Forcer la navigation vers la page de connexion avec un délai
                                    await page.wait_for_timeout(2000)
                                    await page.goto("https://myselogerpro.com/login", timeout=30000)
                                    await page.wait_for_load_state("networkidle", timeout=10000)

                            navigation_time = time.time() - navigation_start
                            self.logger.info(f"Navigation réussie en {navigation_time:.2f} secondes")

                        except Exception as e:
                            self.logger.error(f"Erreur de connexion à SeLogerPro: {e}")

                            # Essayer avec une URL alternative
                            try:
                                self.logger.info("Tentative avec URL alternative...")
                                await page.goto("https://selogerpro.com", timeout=30000)
                                await page.wait_for_load_state("domcontentloaded", timeout=10000)

                                # Rechercher un lien de connexion
                                login_selectors = [
                                    "a:has-text('Connexion')",
                                    "a:has-text('Se connecter')",
                                    "a:has-text('Login')",
                                    "button:has-text('Connexion')"
                                ]

                                for selector in login_selectors:
                                    if await page.is_visible(selector, timeout=1000):
                                        self.logger.info(f"Lien de connexion trouvé: {selector}")
                                        await page.click(selector)
                                        await page.wait_for_load_state("networkidle", timeout=10000)
                                        break

                                navigation_time = time.time() - navigation_start

                            except Exception as alt_error:
                                self.logger.error(f"Échec avec l'URL alternative: {alt_error}")
                                self.logger.info("Utilisation du mode de secours (fallback)")
                                # Fallback: simuler la navigation
                                navigation_time = time.time() - navigation_start

                                # Capture d'écran en cas d'erreur
                                if self.config["error_handling"]["error_logs"]["screenshot_on_error"]:
                                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                                    try:
                                        await page.screenshot(path=f"screenshots/navigation_error_{timestamp}.png")
                                    except Exception:
                                        pass

                            # Après l'erreur de navigation, on simule le reste du processus
                            is_logged_in = False
                            login_time = 0
                            search_time = 0
                            expired_properties = []
                            total_renewal_time = 0
                            avg_renewal_time = 0
                            renewal_times = []

                            # On continue avec le reste du code pour éviter les erreurs
                            self.logger.info("Simulation du processus après échec de connexion")
                        else:
                            # Si la navigation a réussi, on continue normalement
                            # Vérification si déjà connecté
                            is_logged_in = False

                            # Vérifier plusieurs indicateurs de connexion
                            login_indicators = [
                                ".user-profile",
                                ".dashboard",
                                ".logged-in",
                                "a:has-text('Déconnexion')",
                                "a:has-text('Mon compte')"
                            ]

                            for indicator in login_indicators:
                                try:
                                    if await page.is_visible(indicator, timeout=1000):
                                        is_logged_in = True
                                        self.logger.info(f"Déjà connecté, indicateur trouvé: {indicator}")
                                        break
                                except Exception:
                                    continue

                            # Vérifier également l'URL
                            current_url = page.url
                            if "login" in current_url:
                                is_logged_in = False
                                self.logger.info("Sur la page de connexion, session expirée")

                            # Connexion si nécessaire
                            if not is_logged_in:
                                self.logger.info("Session expirée ou non chargée, nouvelle connexion")
                                login_start = time.time()
                                login_success = await self.login(page)
                                login_time = time.time() - login_start

                                # Vérifier si la connexion a réussi
                                if not login_success:
                                    self.logger.error("Échec de la connexion, tentative de nettoyage et de reconnexion")

                                    # Nettoyer les cookies et réessayer
                                    await context.clear_cookies()
                                    await page.reload()
                                    await page.wait_for_load_state("networkidle", timeout=10000)

                                    # Deuxième tentative de connexion
                                    login_success = await self.login(page)
                                    if not login_success:
                                        self.logger.error("Échec de la deuxième tentative de connexion")
                                        # Continuer avec le reste du code, mais les opérations échoueront probablement

                                if login_success:
                                    # Sauvegarde de l'état d'authentification
                                    try:
                                        await self.save_authentication_state(
                                            context,
                                            self.config["auth"]["password"]
                                        )
                                    except Exception as auth_save_error:
                                        self.logger.error(f"Erreur lors de la sauvegarde de l'état d'authentification: {auth_save_error}")
                                        # Continuer malgré l'erreur
                                else:
                                    self.logger.error("Échec de la connexion")
                                    # On simule le reste du processus au lieu de lever une exception
                                    expired_properties = []
                                    search_time = 0
                                    total_renewal_time = 0
                                    avg_renewal_time = 0
                                    renewal_times = []
                            else:
                                login_time = 0

                                try:
                                    # Navigation vers la page des annonces
                                    await page.goto("https://www.selogerpro.com/mes-annonces", timeout=30000)
                                    await page.wait_for_load_state("networkidle")

                                    # Vérifier si l'utilisateur est bloqué après navigation
                                    if await self.check_for_blocking(page):
                                        self.logger.error("Blocage détecté après navigation vers la page des annonces")
                                        results["blocked"] = True
                                        results["errors"].append("Blocage détecté par le site")

                                        # Prendre une capture d'écran finale du blocage
                                        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                                        await page.screenshot(path=f"screenshots/final_blocked_{timestamp}.png")

                                        # Vérifier si c'est le blocage critique spécifique de SeLogerPro
                                        current_url = page.url
                                        if "myselogerpro.com/login" in current_url:
                                            try:
                                                # Vérifier si c'est la page de blocage spécifique
                                                is_critical_block = await page.evaluate("""() => {
                                                    return document.body.innerText.includes('Vous avez été bloqué(e)') &&
                                                           document.body.innerText.includes('Pourquoi ce blocage');
                                                }""")

                                                if is_critical_block:
                                                    self.logger.critical("ARRÊT DE L'AUTOMATISATION: Blocage critique détecté")
                                                    results["critical_block"] = True
                                                    results["errors"].append("Blocage critique - automatisation arrêtée")
                                            except Exception:
                                                pass

                                        return results

                                    # Recherche des propriétés expirées
                                    search_start = time.time()
                                    expired_properties = await self.find_expired_properties(page)
                                    search_time = time.time() - search_start

                                    # Application de la limite si configurée
                                    max_properties = self.config["renewal"]["max_properties_per_run"]
                                    if max_properties > 0 and len(expired_properties) > max_properties:
                                        self.logger.info(f"Limitation à {max_properties} propriétés sur {len(expired_properties)}")
                                        expired_properties = expired_properties[:max_properties]

                                    # Renouvellement des propriétés
                                    renewal_start = time.time()
                                    renewal_times = []

                                    for prop_id in expired_properties:
                                        prop_start_time = time.time()

                                        # Ajout d'un délai aléatoire pour éviter la détection
                                        delay = random.uniform(
                                            self.config["renewal"]["schedule"]["random_delay"]["min_seconds"],
                                            self.config["renewal"]["schedule"]["random_delay"]["max_seconds"]
                                        )
                                        await asyncio.sleep(delay)

                                        # Vérifier si nous sommes toujours connectés avant de renouveler
                                        current_url = page.url
                                        if "login" in current_url:
                                            self.logger.warning("Déconnexion détectée pendant le renouvellement, tentative de reconnexion...")

                                            # Capture d'écran pour débogage
                                            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                                            await page.screenshot(path=f"screenshots/disconnected_during_renewal_{timestamp}.png")

                                            # Tentative de reconnexion
                                            login_success = await self.login(page)

                                            if login_success:
                                                self.logger.info("Reconnexion réussie, reprise du renouvellement")
                                                # Retourner à la page des annonces
                                                await page.goto("https://www.selogerpro.com/mes-annonces", timeout=30000)
                                                await page.wait_for_load_state("networkidle", timeout=15000)
                                            else:
                                                self.logger.error("Échec de la reconnexion pendant le renouvellement")
                                                results["failed"].append(prop_id)
                                                results["errors"].append("Déconnexion pendant le renouvellement")
                                                continue

                                        # Utilisation du circuit breaker
                                        try:
                                            success = await self.circuit_breaker.execute(
                                                self.renew_property, page, prop_id
                                            )

                                            if success:
                                                results["renewed"].append(prop_id)
                                            else:
                                                results["failed"].append(prop_id)

                                                # Vérifier si l'échec est dû à une déconnexion
                                                current_url = page.url
                                                if "login" in current_url:
                                                    self.logger.warning("Déconnexion détectée après échec de renouvellement")

                                                    # Tentative de reconnexion
                                                    login_success = await self.login(page)

                                                    if login_success:
                                                        self.logger.info("Reconnexion réussie après échec de renouvellement")
                                                        # Retourner à la page des annonces pour les prochaines propriétés
                                                        await page.goto("https://www.selogerpro.com/mes-annonces", timeout=30000)
                                                        await page.wait_for_load_state("networkidle", timeout=15000)
                                        except Exception as e:
                                            self.logger.error(f"Circuit breaker déclenché pour {prop_id}: {e}")
                                            results["failed"].append(prop_id)
                                            results["errors"].append(str(e))

                                            # Capture d'écran en cas d'erreur
                                            if self.config["error_handling"]["error_logs"]["screenshot_on_error"]:
                                                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                                                await page.screenshot(path=f"{screenshots_dir}/error_{prop_id}_{timestamp}.png")

                                        # Enregistrement du temps de traitement pour cette propriété
                                        prop_time = time.time() - prop_start_time
                                        renewal_times.append(prop_time)

                                    total_renewal_time = time.time() - renewal_start
                                    avg_renewal_time = sum(renewal_times) / len(renewal_times) if renewal_times else 0
                                except Exception as nav_error:
                                    self.logger.error(f"Erreur lors de la navigation ou du renouvellement: {nav_error}")
                                    # Initialisation des variables en cas d'erreur
                                    expired_properties = []
                                    search_time = 0
                                    total_renewal_time = 0
                                    avg_renewal_time = 0
                                    renewal_times = []

                    except Exception as e:
                        self.logger.error(f"Erreur dans le workflow: {e}")
                        results["errors"].append(str(e))

                        # Capture d'écran en cas d'erreur
                        if self.config["error_handling"]["error_logs"]["screenshot_on_error"]:
                            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                            try:
                                await page.screenshot(path=f"{screenshots_dir}/error_global_{timestamp}.png")
                            except Exception as screenshot_error:
                                self.logger.error(f"Impossible de prendre une capture d'écran: {screenshot_error}")

                    finally:
                        await browser.close()
        except Exception as e:
            self.logger.error(f"Erreur critique lors de l'initialisation de Playwright: {e}")
            results["errors"].append(f"Erreur critique: {str(e)}")

        # Calcul des métriques de performance
        total_time = time.time() - start_time
        memory_end = self._get_memory_usage()
        memory_usage = memory_end - memory_start

        # Ajout des métriques de performance aux résultats
        if "performance_metrics" in self.config["logging"] and self.config["logging"]["performance_metrics"]["track"]:
            metrics_to_track = self.config["logging"]["performance_metrics"]["track"]

            if "execution_time" in metrics_to_track:
                results["performance_metrics"]["execution_time"] = total_time
                results["performance_metrics"]["browser_launch_time"] = browser_launch_time if 'browser_launch_time' in locals() else None
                results["performance_metrics"]["auth_time"] = auth_time if 'auth_time' in locals() else None
                results["performance_metrics"]["navigation_time"] = navigation_time if 'navigation_time' in locals() else None
                results["performance_metrics"]["login_time"] = login_time if 'login_time' in locals() else None
                results["performance_metrics"]["search_time"] = search_time if 'search_time' in locals() else None
                results["performance_metrics"]["total_renewal_time"] = total_renewal_time if 'total_renewal_time' in locals() else None
                results["performance_metrics"]["avg_renewal_time"] = avg_renewal_time if 'avg_renewal_time' in locals() else None

            if "memory_usage" in metrics_to_track:
                results["performance_metrics"]["memory_usage_mb"] = memory_usage

            if "network_latency" in metrics_to_track:
                # Simulation de latence réseau (dans un cas réel, serait mesuré pendant les requêtes)
                results["performance_metrics"]["network_latency_ms"] = 150

        results["end_time"] = datetime.datetime.now().isoformat()
        results["total_renewed"] = len(results["renewed"])
        results["total_failed"] = len(results["failed"])

        # Log des résultats
        self.logger.info(f"Renouvellement terminé: {results['total_renewed']} réussis, {results['total_failed']} échoués")

        # Envoi des résultats au webhook si configuré
        if "webhook" in self.config["notifications"] and (
            not self.config["notifications"]["webhook"]["on_error_only"] or
            results["total_failed"] > 0 or
            len(results["errors"]) > 0
        ):
            self._send_webhook_notification(results)

        # Sauvegarde des logs dans le cloud si configuré
        if "cloud" in self.config["logging"]:
            self._upload_logs_to_cloud(results)

        return results

    def _send_webhook_notification(self, results: Dict[str, Any]) -> None:
        """Envoie une notification webhook avec les résultats"""
        # Vérifier si les webhooks sont désactivés en mode simulation
        if self.simulation_mode:
            self.logger.info("Mode simulation: Notification webhook simulée")
            return

        # Vérifier si l'URL est une URL de test
        webhook_config = self.config["notifications"]["webhook"]
        webhook_url = webhook_config["url"]

        if webhook_url.startswith("https://ton-webapp.com"):
            self.logger.warning("URL webhook par défaut détectée. Notification non envoyée.")
            self.logger.info("Pour activer les webhooks, veuillez configurer une URL valide dans config.json")
            return

        try:
            payload = {
                "timestamp": datetime.datetime.now().isoformat(),
                "results": results
            }

            # Inclusion des logs si configuré
            if webhook_config["include_logs_in_webhook"]:
                # Récupération des dernières entrées de log
                payload["logs"] = self._get_recent_logs()

            # Envoi de la requête avec un timeout court
            self.logger.info(f"Envoi de la notification webhook à {webhook_url}")
            response = requests.post(
                webhook_url,
                json=payload,
                headers=webhook_config["headers"],
                timeout=5  # Timeout réduit à 5 secondes
            )

            if response.status_code >= 200 and response.status_code < 300:
                self.logger.info(f"Notification webhook envoyée avec succès: {response.status_code}")
            else:
                self.logger.error(f"Échec de l'envoi de la notification webhook: {response.status_code}, {response.text}")

        except requests.exceptions.ConnectTimeout:
            self.logger.error(f"Timeout lors de la connexion au webhook {webhook_url}")
        except requests.exceptions.ConnectionError:
            self.logger.error(f"Impossible de se connecter au webhook {webhook_url}")
        except Exception as e:
            self.logger.error(f"Erreur lors de l'envoi de la notification webhook: {e}")

    def _get_recent_logs(self) -> List[str]:
        """Récupère les entrées de log récentes"""
        # Simulation - dans un cas réel, récupérerait les logs du handler
        return ["Log entry 1", "Log entry 2", "Log entry 3"]

    def _upload_logs_to_cloud(self, results: Dict[str, Any]) -> None:
        """Téléverse les logs vers le stockage cloud configuré"""
        # Vérifier si le téléversement est désactivé en mode simulation
        if self.simulation_mode:
            self.logger.info("Mode simulation: Téléversement des logs simulé")
            return

        # Vérifier si le bucket est un bucket de test
        cloud_config = self.config["logging"]["cloud"]
        bucket_name = cloud_config["bucket"]

        if bucket_name == "ton-log-bucket":
            self.logger.warning("Bucket S3 par défaut détecté. Téléversement non effectué.")
            self.logger.info("Pour activer le téléversement S3, veuillez configurer un bucket valide dans config.json")
            return

        try:
            if cloud_config["provider"] == "aws_s3":
                self.logger.info(f"Tentative de téléversement des logs vers S3 (bucket: {bucket_name})")

                try:
                    # Création du client S3 avec un timeout court
                    s3_client = boto3.client(
                        's3',
                        config=boto3.config.Config(
                            connect_timeout=5,
                            retries={'max_attempts': 2}
                        )
                    )

                    # Préparation du contenu
                    log_content = json.dumps({
                        "timestamp": datetime.datetime.now().isoformat(),
                        "results": results,
                        "detailed_logs": self._get_recent_logs()
                    }, indent=2)

                    # Génération du nom de fichier
                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    file_key = f"{cloud_config['path']}{timestamp}_renewal_log.json"

                    # Téléversement
                    s3_client.put_object(
                        Bucket=bucket_name,
                        Key=file_key,
                        Body=log_content
                    )

                    self.logger.info(f"Logs téléversés vers S3: {file_key}")
                except boto3.exceptions.NoCredentialsError:
                    self.logger.error("Aucune information d'identification AWS trouvée")
                    self.logger.info("Pour utiliser S3, configurez vos identifiants AWS via AWS CLI ou variables d'environnement")
                except boto3.exceptions.EndpointConnectionError:
                    self.logger.error("Impossible de se connecter à AWS S3")
                except Exception as s3_error:
                    self.logger.error(f"Erreur S3: {s3_error}")

                # Sauvegarde locale en cas d'échec du téléversement
                logs_dir = "logs"
                if not os.path.exists(logs_dir):
                    os.makedirs(logs_dir)

                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                local_log_file = f"{logs_dir}/renewal_log_{timestamp}.json"

                try:
                    with open(local_log_file, 'w') as f:
                        json.dump({
                            "timestamp": datetime.datetime.now().isoformat(),
                            "results": results,
                            "detailed_logs": self._get_recent_logs()
                        }, f, indent=2)
                    self.logger.info(f"Logs sauvegardés localement: {local_log_file}")
                except Exception as local_error:
                    self.logger.error(f"Erreur lors de la sauvegarde locale des logs: {local_error}")
            else:
                self.logger.warning(f"Fournisseur cloud non pris en charge: {cloud_config['provider']}")
                self.logger.info("Seul AWS S3 est pris en charge pour le moment")

        except Exception as e:
            self.logger.error(f"Erreur lors du téléversement des logs: {e}")

    def _should_renew_property(self, property_age_days: int) -> bool:
        """Détermine si une propriété doit être renouvelée en fonction de son âge"""
        threshold = self.config["renewal"]["listing_age_threshold_days"]
        return property_age_days >= threshold

    async def _solve_captcha_with_2captcha(self, page: Page) -> bool:
        """Résout un captcha slider en utilisant le service 2Captcha"""
        try:
            self.logger.info("Tentative de résolution du captcha avec 2Captcha...")

            # Simuler un comportement humain sur la page avant de résoudre le captcha
            await self.simulate_human_behavior(page)

            # Vérifier si la configuration 2Captcha est disponible
            if not self.config.get("auth", {}).get("captcha", {}).get("api_key"):
                self.logger.warning("Clé API 2Captcha non configurée")
                return False

            # Vérifier si nous avons un captcha dans un iframe
            iframe_captcha = await page.is_visible("iframe[src*='captcha']", timeout=1000)
            frame = None

            # Vérifier si le captcha est toujours présent
            if not iframe_captcha:
                # Vérifier d'autres indicateurs de captcha
                captcha_indicators = [
                    "div:has-text('Verification Required')",
                    "div:has-text('Slide right to complete')",
                    "div:has-text('Slide right')",
                    "div:has-text('puzzle')",
                    "div.slider-captcha",
                    ".slider-puzzle",
                    "button.slider-btn",
                    ".captcha-slider",
                    ".slider"
                ]

                captcha_present = False
                for indicator in captcha_indicators:
                    try:
                        if await page.is_visible(indicator, timeout=1000):
                            captcha_present = True
                            self.logger.info(f"Captcha détecté avec l'indicateur: {indicator}")
                            break
                    except Exception:
                        continue

                if not captcha_present:
                    self.logger.info("Aucun captcha détecté sur la page, vérification si nous sommes déjà connectés...")
                    # Vérifier si nous sommes sur la page de connexion
                    if "login" not in page.url:
                        self.logger.info("Nous ne sommes pas sur la page de connexion, captcha probablement déjà résolu!")
                        return True
                    else:
                        self.logger.info("Nous sommes sur la page de connexion mais aucun captcha n'est visible")

            if iframe_captcha:
                self.logger.info("Captcha dans iframe détecté, tentative de capture...")
                # Obtenir l'iframe
                iframe = await page.query_selector("iframe[src*='captcha']")
                if iframe:
                    frame = await iframe.content_frame()
                    if not frame:
                        self.logger.warning("Impossible d'accéder au contenu de l'iframe")
                    else:
                        self.logger.info("Accès au contenu de l'iframe réussi")

                        # Essayer de trouver le slider dans l'iframe
                        slider_selectors = [
                            "button.slider-btn",
                            ".slider-button",
                            ".slider-handle",
                            "div.slider",
                            "div[role='slider']",
                            "div.sliderContainer",
                            "div.slider-container",
                            "div.slider-btn",
                            "div.sliderBtn",
                            "div.captcha-slider",
                            "div.puzzle-slider-button",
                            ".captcha-slider",
                            ".slider"
                        ]

                        for selector in slider_selectors:
                            try:
                                if await frame.is_visible(selector, timeout=1000):
                                    self.logger.info(f"Slider trouvé dans l'iframe avec le sélecteur: {selector}")
                                    break
                            except Exception:
                                continue

            # Capture d'écran du captcha
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            screenshot_path = f"screenshots/captcha_for_2captcha_{timestamp}.png"

            # Les iframes ne peuvent pas être capturées directement avec Playwright
            # Nous allons donc toujours capturer la page entière
            screenshot_bytes = await page.screenshot(path=screenshot_path)
            self.logger.info("Capture d'écran de la page entière pour le captcha")

            # Essayer d'abord avec notre solution de détection d'image
            try:
                self.logger.info("Tentative de résolution avec notre solveur d'image...")

                # Convertir le screenshot en base64
                screenshot_base64 = base64.b64encode(screenshot_bytes).decode('utf-8')

                # Chercher l'élément puzzle piece (tile)
                puzzle_piece_selectors = [
                    ".slider-puzzle-piece",
                    ".puzzle-piece",
                    ".captcha-piece",
                    "img[alt='puzzle piece']",
                    ".jigsaw-piece",
                    ".puzzle-slider-piece",
                    ".captcha-puzzle",
                    ".jigsaw",
                    "canvas.puzzle",
                    "div.puzzle-piece",
                    "div.captcha-piece",
                    "img[class*='puzzle']",
                    "img[class*='captcha']",
                    "div[class*='puzzle']",
                    "div[class*='captcha']"
                ]

                tile_element = None
                tile_base64 = None

                # Chercher d'abord dans la page principale
                for selector in puzzle_piece_selectors:
                    try:
                        if await page.is_visible(selector, timeout=1000):
                            tile_element = await page.query_selector(selector)
                            if tile_element:
                                self.logger.info(f"Élément puzzle trouvé dans la page principale avec le sélecteur: {selector}")
                                tile_screenshot = await tile_element.screenshot()
                                tile_base64 = base64.b64encode(tile_screenshot).decode('utf-8')
                                break
                    except Exception as e:
                        self.logger.debug(f"Erreur lors de la recherche de l'élément puzzle avec le sélecteur {selector}: {e}")

                # Si on n'a pas trouvé dans la page principale, chercher dans l'iframe
                if not tile_element and frame:
                    for selector in puzzle_piece_selectors:
                        try:
                            if await frame.is_visible(selector, timeout=1000):
                                tile_element = await frame.query_selector(selector)
                                if tile_element:
                                    self.logger.info(f"Élément puzzle trouvé dans l'iframe avec le sélecteur: {selector}")
                                    tile_screenshot = await tile_element.screenshot()
                                    tile_base64 = base64.b64encode(tile_screenshot).decode('utf-8')
                                    break
                        except Exception as e:
                            self.logger.debug(f"Erreur lors de la recherche de l'élément puzzle dans l'iframe avec le sélecteur {selector}: {e}")

                # Initialiser notre solveur de captcha
                captcha_solver = CaptchaSolver()
                captcha_solver.enable_debug(True)

                # Résoudre le captcha (avec ou sans élément puzzle)
                x_position = None
                if tile_base64:
                    self.logger.info("Résolution avec l'élément puzzle détecté")
                    x_position = captcha_solver.solve_slider_captcha(screenshot_base64, tile_base64)
                else:
                    self.logger.info("Résolution sans élément puzzle (détection automatique)")
                    x_position = captcha_solver.solve_slider_captcha(screenshot_base64)

                if x_position:
                    self.logger.info(f"Position détectée par notre solveur: {x_position}")

                    # Stocker les coordonnées pour les utiliser plus tard
                    y_position = await page.evaluate('window.innerHeight') / 2
                    coordinates = [f"{int(x_position)},{int(y_position)}"]
                    self.logger.info(f"Coordonnées obtenues de notre solveur: {coordinates}")

                    # Trouver le slider pour le déplacer
                    slider_found = False
                    slider_element = None
                    slider_box = None

                    # Chercher le slider dans la page principale
                    slider_selectors = [
                        "button.slider-btn",
                        ".slider-button",
                        ".slider-handle",
                        "div.slider",
                        "div[role='slider']",
                        "div.sliderContainer",
                        "div.slider-container",
                        "div.slider-btn",
                        "div.sliderBtn",
                        "div.captcha-slider",
                        "div.puzzle-slider-button",
                        ".captcha-slider",
                        ".slider",
                        ".sliderknob",
                        ".slider-knob",
                        ".slider-thumb",
                        ".captcha-slider-button",
                        "div[class*='slider']",
                        "div[class*='Slider']"
                    ]

                    for selector in slider_selectors:
                        try:
                            if await page.is_visible(selector, timeout=1000):
                                slider_element = await page.query_selector(selector)
                                if slider_element:
                                    slider_box = await slider_element.bounding_box()
                                    if slider_box:
                                        self.logger.info(f"Slider trouvé dans la page principale avec le sélecteur: {selector}")
                                        slider_found = True
                                        break
                        except Exception as e:
                            self.logger.debug(f"Erreur lors de la recherche du slider avec le sélecteur {selector}: {e}")

                    # Si on n'a pas trouvé dans la page principale, chercher dans l'iframe
                    if not slider_found and frame:
                        for selector in slider_selectors:
                            try:
                                if await frame.is_visible(selector, timeout=1000):
                                    slider_element = await frame.query_selector(selector)
                                    if slider_element:
                                        slider_box = await slider_element.bounding_box()
                                        if slider_box:
                                            self.logger.info(f"Slider trouvé dans l'iframe avec le sélecteur: {selector}")
                                            slider_found = True
                                            break
                            except Exception as e:
                                self.logger.debug(f"Erreur lors de la recherche du slider dans l'iframe avec le sélecteur {selector}: {e}")

                    # Si on a trouvé le slider, le déplacer
                    if slider_found and slider_box:
                        try:
                            # Calculer les coordonnées de départ (centre du slider)
                            start_x = slider_box["x"] + slider_box["width"] / 2
                            start_y = slider_box["y"] + slider_box["height"] / 2

                            # Calculer les coordonnées d'arrivée (position détectée)
                            # Ajuster la position horizontale en fonction de la largeur du slider
                            # pour éviter de dépasser les limites
                            end_x = min(x_position, slider_box["x"] + slider_box["width"] * 5)
                            end_y = start_y  # Garder la même hauteur

                            self.logger.info(f"Déplacement du slider de ({start_x}, {start_y}) à ({end_x}, {end_y})")

                            # Déplacer le slider
                            # Note: Les objets Frame n'ont pas d'attribut mouse, nous devons utiliser page.mouse
                            # et calculer les coordonnées correctement

                            # Obtenir les coordonnées de l'iframe si nécessaire
                            iframe_box = None
                            if frame and slider_found:
                                try:
                                    # Trouver l'élément iframe
                                    iframe_element = await page.query_selector("iframe[src*='captcha']")
                                    if iframe_element:
                                        iframe_box = await iframe_element.bounding_box()
                                        self.logger.info(f"Position de l'iframe: {iframe_box}")
                                except Exception as e:
                                    self.logger.error(f"Erreur lors de la recherche de l'iframe: {e}")

                            # Calculer les coordonnées absolues en tenant compte de l'iframe
                            if iframe_box:
                                # Ajuster les coordonnées pour l'iframe
                                abs_start_x = iframe_box["x"] + start_x
                                abs_start_y = iframe_box["y"] + start_y
                                abs_end_x = iframe_box["x"] + end_x
                                abs_end_y = iframe_box["y"] + start_y
                                self.logger.info(f"Coordonnées ajustées pour l'iframe: de ({abs_start_x}, {abs_start_y}) à ({abs_end_x}, {abs_end_y})")
                            else:
                                # Utiliser les coordonnées telles quelles
                                abs_start_x = start_x
                                abs_start_y = start_y
                                abs_end_x = end_x
                                abs_end_y = start_y

                            # Déplacer le slider avec la souris de la page principale
                            try:
                                # Cliquer et maintenir
                                await page.mouse.move(abs_start_x, abs_start_y)
                                await page.mouse.down()

                                # Déplacer progressivement pour simuler un mouvement humain très réaliste
                                steps = random.randint(25, 35)  # Nombre de pas variable

                                # Fonction pour créer une courbe de mouvement plus naturelle
                                def ease_out_cubic(t):
                                    return 1 - pow(1 - t, 3)

                                def ease_in_out_quad(t):
                                    return 2 * t * t if t < 0.5 else 1 - pow(-2 * t + 2, 2) / 2

                                # Choisir aléatoirement une fonction d'accélération
                                ease_func = random.choice([ease_out_cubic, ease_in_out_quad])

                                # Ajouter une légère hésitation au début (mouvement plus lent)
                                await page.wait_for_timeout(random.randint(200, 500))

                                # Mouvement initial lent
                                for i in range(1, 5):
                                    small_step = i / 10
                                    await page.mouse.move(
                                        abs_start_x + ((abs_end_x - abs_start_x) * small_step),
                                        abs_start_y + random.uniform(-1, 1),
                                        steps=3
                                    )
                                    await page.wait_for_timeout(random.randint(50, 150))

                                # Mouvement principal avec accélération naturelle
                                for i in range(1, steps + 1):
                                    progress = i / steps
                                    eased_progress = ease_func(progress)

                                    # Ajouter une légère courbe au mouvement (non linéaire)
                                    vertical_offset = random.uniform(-3, 3)
                                    if progress < 0.3:
                                        # Au début, tendance à aller légèrement vers le haut
                                        vertical_offset = random.uniform(-3, 1)
                                    elif progress > 0.7:
                                        # À la fin, tendance à aller légèrement vers le bas
                                        vertical_offset = random.uniform(-1, 3)

                                    await page.mouse.move(
                                        abs_start_x + ((abs_end_x - abs_start_x) * eased_progress),
                                        abs_start_y + vertical_offset,
                                        steps=random.randint(3, 7)  # Variation dans la fluidité
                                    )

                                    # Pauses variables (plus longues au début et à la fin)
                                    if progress < 0.2 or progress > 0.8:
                                        await page.wait_for_timeout(random.randint(30, 100))
                                    else:
                                        await page.wait_for_timeout(random.randint(10, 50))

                                    # Parfois, ajouter une petite pause aléatoire pour simuler une hésitation
                                    if random.random() < 0.1:  # 10% de chance
                                        await page.wait_for_timeout(random.randint(100, 300))

                                # Légère correction finale pour s'assurer d'arriver exactement à la position cible
                                await page.mouse.move(
                                    abs_end_x,
                                    abs_start_y + random.uniform(-1, 1),
                                    steps=3
                                )
                                await page.wait_for_timeout(random.randint(50, 150))

                                # Relâcher
                                await page.mouse.up()

                                self.logger.info(f"Déplacement du slider effectué de ({abs_start_x}, {abs_start_y}) à ({abs_end_x}, {abs_end_y})")
                            except Exception as e:
                                self.logger.error(f"Erreur lors du déplacement du slider: {e}")

                            # Attendre un peu pour voir si le captcha est résolu
                            await page.wait_for_timeout(2000)

                            # Vérifier si le captcha a disparu
                            captcha_still_visible = await page.is_visible("iframe[src*='captcha']", timeout=1000)
                            if not captcha_still_visible:
                                self.logger.info("Captcha résolu avec succès via notre solveur d'image!")
                                return True
                            else:
                                self.logger.warning("Le captcha est toujours présent après la résolution avec notre solveur")
                        except Exception as e:
                            self.logger.error(f"Erreur lors du déplacement du slider: {e}")
                    else:
                        self.logger.warning("Impossible de trouver le slider pour déplacer à la position détectée")
                else:
                    self.logger.warning("Notre solveur n'a pas pu déterminer la position du puzzle")
            except Exception as e:
                self.logger.error(f"Erreur lors de la résolution avec notre solveur d'image: {e}")

            # Si notre solution a échoué, utiliser 2Captcha comme solution de secours
            self.logger.info("Utilisation de 2Captcha comme solution de secours...")
            solver = TwoCaptcha(self.config["auth"]["captcha"]["api_key"])

            # Nombre maximum de tentatives pour cette session de résolution
            max_attempts = 3  # Réduire le nombre de tentatives par session (nous avons déjà une boucle externe)

            for attempt in range(1, max_attempts + 1):
                try:
                    # Vérifier si le captcha est toujours présent avant chaque tentative
                    captcha_still_present = await page.is_visible("iframe[src*='captcha']", timeout=1000)

                    # Vérifier si le captcha a expiré
                    captcha_expired = False
                    refresh_button_selectors = [
                        "button.refresh-button",
                        "button.captcha-refresh",
                        ".refresh-icon",
                        "button:has-text('Réessayer')",
                        "button:has-text('RÉESSAYER')",
                        "img[alt='refresh']",
                        ".captcha-refresh-icon"
                    ]

                    # Prendre une capture d'écran pour vérifier visuellement l'état du captcha
                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    await page.screenshot(path=f"screenshots/captcha_check_{timestamp}.png")

                    # Vérifier si un bouton de rafraîchissement est visible
                    for selector in refresh_button_selectors:
                        try:
                            if await page.is_visible(selector, timeout=1000):
                                captcha_expired = True
                                self.logger.info(f"Bouton de rafraîchissement du captcha détecté avec le sélecteur: {selector}")
                                # Cliquer sur le bouton de rafraîchissement
                                await page.click(selector)
                                self.logger.info("Clic sur le bouton de rafraîchissement du captcha")
                                await page.wait_for_timeout(2000)  # Attendre que le nouveau captcha se charge
                                break
                        except Exception:
                            continue

                    # Si le captcha a expiré mais qu'aucun bouton n'a été trouvé, essayer de cliquer sur l'image du captcha
                    if not captcha_expired:
                        # Vérifier si l'image du captcha contient une icône de rafraîchissement (comme dans l'image fournie)
                        try:
                            # Prendre une capture d'écran du captcha pour analyse
                            captcha_image_selectors = [
                                "img[src*='captcha']",
                                "iframe[src*='captcha']",
                                ".captcha-image",
                                ".captcha-container img"
                            ]

                            for selector in captcha_image_selectors:
                                if await page.is_visible(selector, timeout=1000):
                                    # Cliquer au centre de l'image du captcha pour le rafraîchir
                                    element = await page.query_selector(selector)
                                    if element:
                                        box = await element.bounding_box()
                                        if box:
                                            center_x = box["x"] + box["width"] / 2
                                            center_y = box["y"] + box["height"] / 2
                                            await page.mouse.click(center_x, center_y)
                                            self.logger.info("Clic au centre de l'image du captcha pour le rafraîchir")
                                            captcha_expired = True
                                            await page.wait_for_timeout(2000)  # Attendre que le nouveau captcha se charge
                                            break
                        except Exception as e:
                            self.logger.debug(f"Erreur lors de la tentative de clic sur l'image du captcha: {e}")

                    if captcha_expired:
                        self.logger.info("Le captcha a été rafraîchi, nouvelle tentative avec le nouveau captcha")
                        # Prendre une nouvelle capture d'écran après le rafraîchissement
                        await page.screenshot(path=f"screenshots/captcha_after_refresh_{timestamp}.png")
                        continue

                    if not captcha_still_present:
                        # Vérifier d'autres indicateurs de captcha
                        captcha_indicators = [
                            "div:has-text('Verification Required')",
                            "div:has-text('Slide right to complete')",
                            "div:has-text('Slide right')",
                            "div:has-text('puzzle')",
                            "div.slider-captcha",
                            ".slider-puzzle",
                            "button.slider-btn",
                            ".captcha-slider",
                            ".slider"
                        ]

                        captcha_present = False
                        for indicator in captcha_indicators:
                            try:
                                if await page.is_visible(indicator, timeout=1000):
                                    captcha_present = True
                                    self.logger.info(f"Captcha toujours présent avec l'indicateur: {indicator}")
                                    break
                            except Exception:
                                continue

                        if not captcha_present:
                            self.logger.info("Le captcha a disparu avant la tentative de résolution!")
                            # Vérifier si nous sommes sur la page de connexion
                            if "login" not in page.url:
                                self.logger.info("Nous ne sommes plus sur la page de connexion, captcha probablement déjà résolu!")

                                # Capture d'écran pour vérifier l'état après résolution du captcha
                                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                                await page.screenshot(path=f"screenshots/captcha_resolved_redirect_{timestamp}.png")

                                # Vérifier si nous sommes sur une page d'erreur
                                error_indicators = [
                                    "div:has-text('Erreur')",
                                    "div:has-text('error')",
                                    "div:has-text('problème')",
                                    "div:has-text('échec')"
                                ]

                                error_detected = False
                                for indicator in error_indicators:
                                    try:
                                        if await page.is_visible(indicator, timeout=1000):
                                            error_detected = True
                                            self.logger.warning(f"Page d'erreur détectée après résolution du captcha: {indicator}")
                                            break
                                    except Exception as e:
                                        self.logger.debug(f"Erreur lors de la vérification de l'indicateur d'erreur {indicator}: {e}")

                                if error_detected:
                                    self.logger.info("Tentative de retour à la page de connexion...")
                                    await page.goto("https://myselogerpro.com/login", timeout=30000)
                                    await page.wait_for_load_state("networkidle", timeout=10000)
                                    return False

                                return True
                            else:
                                self.logger.info("Nous sommes sur la page de connexion mais le captcha a disparu")

                                # Vérifier si le formulaire de connexion est visible
                                login_form_visible = False
                                login_form_selectors = [
                                    "input[type='password']",
                                    "button:has-text('Me connecter')",
                                    "button:has-text('Connexion')",
                                    "form.login-form"
                                ]

                                for selector in login_form_selectors:
                                    try:
                                        if await page.is_visible(selector, timeout=1000):
                                            login_form_visible = True
                                            self.logger.info(f"Formulaire de connexion détecté: {selector}")
                                            break
                                    except Exception as e:
                                        self.logger.debug(f"Erreur lors de la vérification du sélecteur de formulaire {selector}: {e}")

                                if login_form_visible:
                                    self.logger.info("Formulaire de connexion visible, tentative de connexion...")
                                    # Continuer avec la connexion
                                else:
                                    self.logger.warning("Formulaire de connexion non visible, tentative de rafraîchissement de la page...")
                                    await page.reload()
                                    await page.wait_for_load_state("networkidle", timeout=10000)

                                # Continuer quand même avec la résolution au cas où

                    # Prendre une nouvelle capture d'écran à chaque tentative
                    new_timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    screenshot_path = f"screenshots/captcha_for_2captcha_attempt_{attempt}_{new_timestamp}.png"
                    await page.screenshot(path=screenshot_path)

                    # Envoyer l'image du captcha à 2Captcha pour résolution
                    self.logger.info(f"Envoi du captcha à 2Captcha (tentative {attempt}/{max_attempts})...")

                    # Essayer différentes méthodes de résolution en fonction de l'attempt
                    try:
                        # Utiliser un timeout plus long pour permettre à 2Captcha de résoudre le captcha
                        # La méthode est synchrone, donc nous devons l'exécuter dans un thread séparé
                        self.logger.info("Attente de la résolution par 2Captcha (peut prendre jusqu'à 3 minutes)...")

                        # Créer une fonction pour exécuter la résolution dans un thread séparé
                        def solve_captcha():
                            # Alterner entre différentes méthodes de résolution
                            if attempt % 3 == 1:
                                # Méthode 1: Coordinates avec instructions détaillées
                                self.logger.info("Utilisation de la méthode coordinates avec instructions détaillées")
                                return solver.coordinates(
                                    screenshot_path,
                                    hintText="IMPORTANT: Trouvez le slider (barre horizontale avec bouton) et cliquez sur l'EXTRÉMITÉ DROITE de la barre où le slider doit s'arrêter. Si vous voyez un bouton 'Je ne suis pas un robot', cliquez dessus à la place.",
                                    # Augmenter le timeout pour donner plus de temps aux travailleurs de 2Captcha
                                    timeout=180
                                )
                            elif attempt % 3 == 2:
                                # Méthode 2: Normal captcha pour décrire ce qu'il faut faire
                                self.logger.info("Utilisation de la méthode normal avec instructions détaillées")
                                return solver.normal(
                                    screenshot_path,
                                    hintText="Ceci est un captcha slider. Décrivez où se trouve le slider et où il faut cliquer. Exemple: 'Cliquer sur le bouton du slider et faire glisser vers la droite' ou 'Cliquer sur le bouton Je ne suis pas un robot'",
                                    # Augmenter le timeout pour donner plus de temps aux travailleurs de 2Captcha
                                    timeout=180
                                )
                            else:
                                # Méthode 3: Coordinates avec instructions simples
                                self.logger.info("Utilisation de la méthode coordinates avec instructions simples")
                                return solver.coordinates(
                                    screenshot_path,
                                    hintText="Cliquez sur l'extrémité DROITE de la barre du slider (là où le slider doit s'arrêter)",
                                    # Augmenter le timeout pour donner plus de temps aux travailleurs de 2Captcha
                                    timeout=180
                                )

                        # Exécuter la résolution dans un thread séparé pour ne pas bloquer la boucle d'événements asyncio
                        loop = asyncio.get_event_loop()
                        result = await loop.run_in_executor(None, solve_captcha)

                        self.logger.info(f"Réponse reçue de 2Captcha: {result}")

                        # Traiter le résultat en fonction de la méthode utilisée
                        if attempt % 3 == 1 or attempt % 3 == 0:  # Méthode coordinates
                            if not result or not result.get("coordinates"):
                                self.logger.error("Pas de coordonnées retournées par 2Captcha")
                                if attempt < max_attempts:
                                    self.logger.info(f"Nouvelle tentative ({attempt+1}/{max_attempts})...")
                                    await page.wait_for_timeout(2000)  # Attendre un peu avant de réessayer
                                    continue
                                return False

                            # Extraire les coordonnées
                            coordinates = result.get("coordinates", [])
                            if not coordinates:
                                self.logger.error("Coordonnées vides retournées par 2Captcha")
                                if attempt < max_attempts:
                                    self.logger.info(f"Nouvelle tentative ({attempt+1}/{max_attempts})...")
                                    await page.wait_for_timeout(2000)  # Attendre un peu avant de réessayer
                                    continue
                                return False

                            # Si nous avons des coordonnées valides, sortir de la boucle de tentatives
                            break
                        else:  # Méthode normal
                            # Pour la méthode normal, nous recevons une description textuelle
                            # Nous allons l'analyser pour déterminer où cliquer
                            if not result or not result.get("text"):
                                self.logger.error("Pas de texte retourné par 2Captcha")
                                if attempt < max_attempts:
                                    self.logger.info(f"Nouvelle tentative ({attempt+1}/{max_attempts})...")
                                    await page.wait_for_timeout(2000)  # Attendre un peu avant de réessayer
                                    continue
                                return False

                            # Analyser le texte pour déterminer où cliquer
                            text_result = result.get("text", "").lower()
                            self.logger.info(f"Texte retourné par 2Captcha: {text_result}")

                            # Simuler des coordonnées basées sur la description
                            if "droite" in text_result or "right" in text_result:
                                # Simuler un clic à l'extrémité droite
                                # Obtenir les dimensions de la page
                                page_width = await page.evaluate("window.innerWidth")
                                page_height = await page.evaluate("window.innerHeight")

                                # Estimer la position du slider (milieu de la page en hauteur, 80% de la largeur)
                                coordinates = [f"{int(page_width * 0.8)},{int(page_height * 0.5)}"]
                                self.logger.info(f"Coordonnées simulées basées sur la description: {coordinates}")
                                break
                            elif "robot" in text_result:
                                # Chercher le bouton "Je ne suis pas un robot"
                                robot_selectors = [
                                    "#recaptcha-anchor",
                                    ".recaptcha-checkbox-border",
                                    "[role='checkbox']",
                                    "button:has-text('Je ne suis pas un robot')",
                                    "button:has-text('I am not a robot')"
                                ]

                                robot_found = False
                                for selector in robot_selectors:
                                    try:
                                        if frame and await frame.is_visible(selector, timeout=1000):
                                            robot_element = await frame.query_selector(selector)
                                            if robot_element:
                                                robot_box = await robot_element.bounding_box()
                                                if robot_box:
                                                    # Calculer le centre du bouton
                                                    robot_x = robot_box["x"] + robot_box["width"] / 2
                                                    robot_y = robot_box["y"] + robot_box["height"] / 2
                                                    coordinates = [f"{int(robot_x)},{int(robot_y)}"]
                                                    self.logger.info(f"Coordonnées du bouton 'Je ne suis pas un robot': {coordinates}")
                                                    robot_found = True
                                                    break
                                        elif await page.is_visible(selector, timeout=1000):
                                            robot_element = await page.query_selector(selector)
                                            if robot_element:
                                                robot_box = await robot_element.bounding_box()
                                                if robot_box:
                                                    # Calculer le centre du bouton
                                                    robot_x = robot_box["x"] + robot_box["width"] / 2
                                                    robot_y = robot_box["y"] + robot_box["height"] / 2
                                                    coordinates = [f"{int(robot_x)},{int(robot_y)}"]
                                                    self.logger.info(f"Coordonnées du bouton 'Je ne suis pas un robot': {coordinates}")
                                                    robot_found = True
                                                    break
                                    except Exception:
                                        continue

                                if robot_found:
                                    break

                            # Si nous n'avons pas pu déterminer où cliquer, essayer une autre méthode
                            self.logger.warning("Impossible de déterminer où cliquer à partir de la description")
                            if attempt < max_attempts:
                                self.logger.info(f"Nouvelle tentative ({attempt+1}/{max_attempts})...")
                                await page.wait_for_timeout(2000)  # Attendre un peu avant de réessayer
                                continue
                            return False

                    except Exception as e:
                        self.logger.error(f"Erreur lors de l'appel à 2Captcha: {e}")
                        if attempt < max_attempts:
                            self.logger.info(f"Nouvelle tentative ({attempt+1}/{max_attempts})...")
                            await page.wait_for_timeout(2000)  # Attendre un peu avant de réessayer
                            continue
                        return False

                except Exception as e:
                    self.logger.error(f"Erreur lors de la tentative {attempt}: {e}")
                    if attempt < max_attempts:
                        self.logger.info(f"Nouvelle tentative ({attempt+1}/{max_attempts})...")
                        await page.wait_for_timeout(2000)  # Attendre un peu avant de réessayer
                        continue
                    return False

            # Si nous arrivons ici, c'est que nous avons des coordonnées valides
            self.logger.info(f"Coordonnées obtenues de 2Captcha: {coordinates}")

            # Trouver le slider dans la page principale
            slider_selectors = [
                "button.slider-btn",
                ".slider-button",
                ".slider-handle",
                "div.slider",
                "div[role='slider']",
                "div.sliderContainer",
                "div.slider-container",
                "div.slider-btn",
                "div.sliderBtn",
                "div.captcha-slider",
                "div.puzzle-slider-button",
                ".captcha-slider",
                ".slider"
            ]

            slider_element = None
            slider_in_page = False
            for selector in slider_selectors:
                try:
                    if await page.is_visible(selector, timeout=1000):
                        slider_element = await page.query_selector(selector)
                        self.logger.info(f"Slider trouvé dans la page principale avec le sélecteur: {selector}")
                        slider_in_page = True
                        break
                except Exception:
                    continue

            # Vérifier si nous avons un iframe captcha
            iframe_captcha = await page.is_visible("iframe[src*='captcha']", timeout=1000)
            frame = None
            slider_in_frame = False
            frame_slider_element = None

            if iframe_captcha:
                self.logger.info("Captcha dans iframe détecté, tentative d'interaction...")
                iframe = await page.query_selector("iframe[src*='captcha']")
                if iframe:
                    frame = await iframe.content_frame()
                    if frame:
                        self.logger.info("Accès au contenu de l'iframe réussi, recherche du slider dans l'iframe...")

                        # Chercher le slider dans l'iframe
                        for selector in slider_selectors:
                            try:
                                if await frame.is_visible(selector, timeout=1000):
                                    frame_slider_element = await frame.query_selector(selector)
                                    self.logger.info(f"Slider trouvé dans l'iframe avec le sélecteur: {selector}")
                                    slider_in_frame = True
                                    break
                            except Exception:
                                continue

            # Utiliser les coordonnées retournées par 2Captcha
            # Format typique: "x,y"
            target_coords = coordinates[0].split(",")
            target_x = float(target_coords[0])
            target_y = float(target_coords[1])

            self.logger.info(f"Coordonnées cibles reçues de 2Captcha: x={target_x}, y={target_y}")

            # Ajuster les coordonnées en fonction de la taille de l'écran
            # Les coordonnées de 2Captcha sont relatives à l'image envoyée
            page_width = await page.evaluate("window.innerWidth")
            page_height = await page.evaluate("window.innerHeight")

            # Obtenir les dimensions de l'image envoyée
            screenshot_info = await page.evaluate("""() => {
                return {
                    width: window.innerWidth,
                    height: window.innerHeight
                }
            }""")

            # Calculer le facteur d'échelle
            scale_x = page_width / screenshot_info["width"]
            scale_y = page_height / screenshot_info["height"]

            # Ajuster les coordonnées
            adjusted_x = target_x * scale_x
            adjusted_y = target_y * scale_y

            self.logger.info(f"Coordonnées ajustées: x={adjusted_x}, y={adjusted_y}")

            # Nombre maximum de tentatives pour le clic
            max_click_attempts = 5  # Augmenter le nombre de tentatives

            # Déterminer la stratégie à utiliser en fonction de ce que nous avons trouvé
            if slider_in_frame:
                self.logger.info("Stratégie: Utiliser le slider dans l'iframe")
                # Obtenir les dimensions du slider dans l'iframe
                frame_slider_box = await frame_slider_element.bounding_box()
                if frame_slider_box:
                    # Calculer les coordonnées de départ (centre du slider)
                    frame_start_x = frame_slider_box["x"] + frame_slider_box["width"] / 2
                    frame_start_y = frame_slider_box["y"] + frame_slider_box["height"] / 2

                    # Calculer les coordonnées d'arrivée (extrémité droite)
                    # Essayer de trouver le conteneur du slider
                    container_selectors = [
                        ".slider-container",
                        ".sliderContainer",
                        ".captcha-slider",
                        ".slider-track",
                        ".slider-rail",
                        ".slider"
                    ]

                    container_width = None
                    for selector in container_selectors:
                        try:
                            container = await frame.query_selector(selector)
                            if container:
                                container_box = await container.bounding_box()
                                if container_box:
                                    container_width = container_box["width"]
                                    self.logger.info(f"Largeur du conteneur dans l'iframe: {container_width}px")
                                    break
                        except Exception:
                            continue

                    # Si on n'a pas trouvé le conteneur, utiliser une valeur par défaut
                    if not container_width:
                        # Essayer de trouver la largeur en utilisant JavaScript
                        try:
                            container_width = await frame.evaluate("""
                                () => {
                                    const sliders = document.querySelectorAll('.slider-container, .sliderContainer, .captcha-slider, .slider-track, .slider-rail, .slider');
                                    if (sliders.length > 0) {
                                        return sliders[0].offsetWidth;
                                    }
                                    return 300; // Valeur par défaut
                                }
                            """)
                            self.logger.info(f"Largeur du conteneur dans l'iframe (via JS): {container_width}px")
                        except Exception:
                            container_width = 300  # Valeur par défaut
                            self.logger.info(f"Utilisation d'une largeur par défaut pour l'iframe: {container_width}px")

                    # Calculer la distance à faire glisser (environ 85-95% de la largeur disponible)
                    # Ajouter une légère variation pour paraître plus humain
                    variation_pct = random.uniform(0.85, 0.95)  # Entre 85% et 95% de la largeur
                    frame_end_x = frame_start_x + container_width * variation_pct
                    # Légère variation verticale
                    frame_end_y = frame_start_y + random.uniform(-3, 3)

                    self.logger.info(f"Déplacement du slider dans l'iframe de ({frame_start_x}, {frame_start_y}) à ({frame_end_x}, {frame_end_y})")
            elif slider_in_page:
                self.logger.info("Stratégie: Utiliser le slider dans la page principale")
                # Obtenir les dimensions du slider dans la page principale
                slider_box = await slider_element.bounding_box()
                if slider_box:
                    # Calculer les coordonnées de départ (centre du slider)
                    start_x = slider_box["x"] + slider_box["width"] / 2
                    start_y = slider_box["y"] + slider_box["height"] / 2

                    # Calculer les coordonnées d'arrivée (extrémité droite)
                    # Essayer de trouver le conteneur du slider
                    container_selectors = [
                        ".slider-container",
                        ".sliderContainer",
                        ".captcha-slider",
                        ".slider-track",
                        ".slider-rail",
                        ".slider"
                    ]

                    container_width = None
                    for selector in container_selectors:
                        try:
                            container = await page.query_selector(selector)
                            if container:
                                container_box = await container.bounding_box()
                                if container_box:
                                    container_width = container_box["width"]
                                    self.logger.info(f"Largeur du conteneur dans la page: {container_width}px")
                                    break
                        except Exception:
                            continue

                    # Si on n'a pas trouvé le conteneur, utiliser une valeur par défaut
                    if not container_width:
                        # Essayer de trouver la largeur en utilisant JavaScript
                        try:
                            container_width = await page.evaluate("""
                                () => {
                                    const sliders = document.querySelectorAll('.slider-container, .sliderContainer, .captcha-slider, .slider-track, .slider-rail, .slider');
                                    if (sliders.length > 0) {
                                        return sliders[0].offsetWidth;
                                    }
                                    return 300; // Valeur par défaut
                                }
                            """)
                            self.logger.info(f"Largeur du conteneur dans la page (via JS): {container_width}px")
                        except Exception:
                            container_width = 300  # Valeur par défaut
                            self.logger.info(f"Utilisation d'une largeur par défaut pour la page: {container_width}px")

                    # Calculer la distance à faire glisser (environ 85-95% de la largeur disponible)
                    # Ajouter une légère variation pour paraître plus humain
                    variation_pct = random.uniform(0.85, 0.95)  # Entre 85% et 95% de la largeur
                    end_x = start_x + container_width * variation_pct
                    # Légère variation verticale
                    end_y = start_y + random.uniform(-3, 3)

                    self.logger.info(f"Déplacement du slider dans la page de ({start_x}, {start_y}) à ({end_x}, {end_y})")
            else:
                self.logger.info("Stratégie: Utiliser les coordonnées retournées par 2Captcha")
                # Nous n'avons pas trouvé de slider, nous allons utiliser les coordonnées retournées par 2Captcha

            for click_attempt in range(1, max_click_attempts + 1):
                try:
                    self.logger.info(f"Tentative de clic/glissement {click_attempt}/{max_click_attempts}...")

                    # Utiliser différentes stratégies en fonction de ce que nous avons trouvé
                    if slider_in_frame and frame:
                        # Stratégie 1: Utiliser le slider dans l'iframe
                        self.logger.info(f"Stratégie 1 (tentative {click_attempt}): Glissement du slider dans l'iframe")

                        try:
                            # Méthode 1: Utiliser mouse.down, mouse.move, mouse.up dans l'iframe
                            if click_attempt % 3 == 1:
                                self.logger.info("Méthode 1: Glissement avec mouse.down, mouse.move, mouse.up dans l'iframe")

                                await frame.mouse.move(frame_start_x, frame_start_y)
                                await frame.mouse.down()

                                # Déplacer progressivement pour simuler un mouvement humain
                                steps = random.randint(15, 25)  # Nombre aléatoire de steps pour plus de naturel
                                distance_x = frame_end_x - frame_start_x
                                distance_y = frame_end_y - frame_start_y

                                # Générer une courbe de mouvement plus naturelle (non linéaire)
                                # Utiliser une courbe de Bézier pour simuler l'accélération et la décélération
                                for i in range(1, steps + 1):
                                    # Facteur de progression non linéaire (accélération puis décélération)
                                    progress = i / steps
                                    # Courbe d'accélération/décélération (fonction easeInOutQuad)
                                    eased_progress = progress * progress * (3 - 2 * progress)

                                    # Ajouter une légère déviation aléatoire pour simuler la main qui tremble
                                    deviation_x = random.uniform(-2, 2) * (1 - abs(2 * progress - 1))  # Plus de déviation au milieu
                                    deviation_y = random.uniform(-3, 3) * (1 - abs(2 * progress - 1))  # Plus de déviation au milieu

                                    await frame.mouse.move(
                                        frame_start_x + (distance_x * eased_progress) + deviation_x,
                                        frame_start_y + (distance_y * eased_progress) + deviation_y,
                                        steps=random.randint(3, 7)  # Variation dans la fluidité du mouvement
                                    )

                                    # Pauses aléatoires avec plus de variation
                                    # Pauses plus longues au début et à la fin pour simuler l'hésitation
                                    if i < 3 or i > steps - 3:
                                        await page.wait_for_timeout(random.randint(50, 150))  # Pause plus longue au début/fin
                                    else:
                                        await page.wait_for_timeout(random.randint(20, 80))  # Pause standard

                                await frame.mouse.up()

                            # Méthode 2: Utiliser JavaScript pour faire glisser le slider dans l'iframe
                            elif click_attempt % 3 == 2:
                                self.logger.info("Méthode 2: Glissement avec JavaScript dans l'iframe")

                                # Utiliser JavaScript pour faire glisser le slider
                                await frame.evaluate("""
                                    (startX, startY, endX, endY) => {
                                        const slider = document.querySelector('.slider-btn, .slider-button, .slider-handle, [role="slider"], .slider, .captcha-slider');
                                        if (!slider) return false;

                                        // Simuler un événement de glissement
                                        const mouseDown = new MouseEvent('mousedown', {
                                            bubbles: true,
                                            cancelable: true,
                                            view: window,
                                            clientX: startX,
                                            clientY: startY
                                        });

                                        slider.dispatchEvent(mouseDown);

                                        // Simuler plusieurs mouvements pour un glissement plus naturel
                                        const steps = Math.floor(Math.random() * 10) + 15; // Entre 15 et 25 steps
                                        const moveEvents = [];

                                        // Fonction d'accélération/décélération (easeInOutQuad)
                                        const easeInOutQuad = (t) => t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2;

                                        for (let i = 1; i <= steps; i++) {
                                            const progress = i / steps;
                                            const eased = easeInOutQuad(progress);

                                            // Ajouter une déviation aléatoire qui est plus prononcée au milieu du mouvement
                                            const midPointFactor = 1 - Math.abs(2 * progress - 1); // 0 au début et à la fin, 1 au milieu
                                            const deviationX = (Math.random() * 4 - 2) * midPointFactor;
                                            const deviationY = (Math.random() * 6 - 3) * midPointFactor;

                                            const stepX = startX + ((endX - startX) * eased) + deviationX;
                                            const stepY = startY + ((endY - startY) * eased) + deviationY;

                                            const mouseMove = new MouseEvent('mousemove', {
                                                bubbles: true,
                                                cancelable: true,
                                                view: window,
                                                clientX: stepX,
                                                clientY: stepY
                                            });

                                            moveEvents.push(mouseMove);
                                        }

                                        // Exécuter les mouvements avec des délais variables
                                        moveEvents.forEach((event, index) => {
                                            // Délais plus longs au début et à la fin pour simuler l'hésitation
                                            let delay;
                                            if (index < 3 || index > steps - 4) {
                                                delay = 50 + Math.random() * 100; // 50-150ms au début/fin
                                            } else {
                                                delay = 20 + Math.random() * 60;  // 20-80ms au milieu
                                            }

                                            setTimeout(() => {
                                                slider.dispatchEvent(event);
                                            }, index * delay);
                                        });

                                        // Simuler le relâchement de la souris
                                        setTimeout(() => {
                                            const mouseUp = new MouseEvent('mouseup', {
                                                bubbles: true,
                                                cancelable: true,
                                                view: window,
                                                clientX: endX,
                                                clientY: endY
                                            });

                                            slider.dispatchEvent(mouseUp);
                                        }, steps * 50 + 100);

                                        return true;
                                    }
                                """, frame_start_x, frame_start_y, frame_end_x, frame_end_y)

                            # Méthode 3: Cliquer directement aux coordonnées retournées par 2Captcha
                            else:
                                self.logger.info("Méthode 3: Clic direct aux coordonnées retournées par 2Captcha dans l'iframe")
                                await frame.mouse.click(target_x, target_y)

                        except Exception as e:
                            self.logger.warning(f"Erreur lors de l'interaction avec l'iframe: {e}")
                            # Fallback: essayer dans la page principale
                            if slider_in_page:
                                self.logger.info("Fallback: Essai avec le slider dans la page principale")
                                await page.mouse.move(start_x, start_y)
                                await page.mouse.down()
                                await page.mouse.move(end_x, end_y, steps=15)
                                await page.mouse.up()
                            else:
                                self.logger.info("Fallback: Clic direct aux coordonnées ajustées dans la page principale")
                                await page.mouse.click(adjusted_x, adjusted_y)

                    elif slider_in_page:
                        # Stratégie 2: Utiliser le slider dans la page principale
                        self.logger.info(f"Stratégie 2 (tentative {click_attempt}): Glissement du slider dans la page principale")

                        try:
                            # Méthode 1: Utiliser mouse.down, mouse.move, mouse.up dans la page principale
                            if click_attempt % 3 == 1:
                                self.logger.info("Méthode 1: Glissement avec mouse.down, mouse.move, mouse.up dans la page principale")

                                await page.mouse.move(start_x, start_y)
                                await page.mouse.down()

                                # Déplacer progressivement pour simuler un mouvement humain
                                steps = random.randint(15, 25)  # Nombre aléatoire de steps pour plus de naturel
                                distance_x = end_x - start_x
                                distance_y = end_y - start_y

                                # Générer une courbe de mouvement plus naturelle (non linéaire)
                                # Utiliser une courbe de Bézier pour simuler l'accélération et la décélération
                                for i in range(1, steps + 1):
                                    # Facteur de progression non linéaire (accélération puis décélération)
                                    progress = i / steps
                                    # Courbe d'accélération/décélération (fonction easeInOutQuad)
                                    eased_progress = progress * progress * (3 - 2 * progress)

                                    # Ajouter une légère déviation aléatoire pour simuler la main qui tremble
                                    deviation_x = random.uniform(-2, 2) * (1 - abs(2 * progress - 1))  # Plus de déviation au milieu
                                    deviation_y = random.uniform(-3, 3) * (1 - abs(2 * progress - 1))  # Plus de déviation au milieu

                                    await page.mouse.move(
                                        start_x + (distance_x * eased_progress) + deviation_x,
                                        start_y + (distance_y * eased_progress) + deviation_y,
                                        steps=random.randint(3, 7)  # Variation dans la fluidité du mouvement
                                    )

                                    # Pauses aléatoires avec plus de variation
                                    # Pauses plus longues au début et à la fin pour simuler l'hésitation
                                    if i < 3 or i > steps - 3:
                                        await page.wait_for_timeout(random.randint(50, 150))  # Pause plus longue au début/fin
                                    else:
                                        await page.wait_for_timeout(random.randint(20, 80))  # Pause standard

                                await page.mouse.up()

                            # Méthode 2: Utiliser JavaScript pour faire glisser le slider dans la page principale
                            elif click_attempt % 3 == 2:
                                self.logger.info("Méthode 2: Glissement avec JavaScript dans la page principale")

                                # Utiliser JavaScript pour faire glisser le slider
                                await page.evaluate("""
                                    (startX, startY, endX, endY) => {
                                        const slider = document.querySelector('.slider-btn, .slider-button, .slider-handle, [role="slider"], .slider, .captcha-slider');
                                        if (!slider) return false;

                                        // Simuler un événement de glissement
                                        const mouseDown = new MouseEvent('mousedown', {
                                            bubbles: true,
                                            cancelable: true,
                                            view: window,
                                            clientX: startX,
                                            clientY: startY
                                        });

                                        slider.dispatchEvent(mouseDown);

                                        // Simuler plusieurs mouvements pour un glissement plus naturel
                                        const steps = Math.floor(Math.random() * 10) + 15; // Entre 15 et 25 steps
                                        const moveEvents = [];

                                        // Fonction d'accélération/décélération (easeInOutQuad)
                                        const easeInOutQuad = (t) => t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2;

                                        for (let i = 1; i <= steps; i++) {
                                            const progress = i / steps;
                                            const eased = easeInOutQuad(progress);

                                            // Ajouter une déviation aléatoire qui est plus prononcée au milieu du mouvement
                                            const midPointFactor = 1 - Math.abs(2 * progress - 1); // 0 au début et à la fin, 1 au milieu
                                            const deviationX = (Math.random() * 4 - 2) * midPointFactor;
                                            const deviationY = (Math.random() * 6 - 3) * midPointFactor;

                                            const stepX = startX + ((endX - startX) * eased) + deviationX;
                                            const stepY = startY + ((endY - startY) * eased) + deviationY;

                                            const mouseMove = new MouseEvent('mousemove', {
                                                bubbles: true,
                                                cancelable: true,
                                                view: window,
                                                clientX: stepX,
                                                clientY: stepY
                                            });

                                            moveEvents.push(mouseMove);
                                        }

                                        // Exécuter les mouvements avec des délais variables
                                        moveEvents.forEach((event, index) => {
                                            // Délais plus longs au début et à la fin pour simuler l'hésitation
                                            let delay;
                                            if (index < 3 || index > steps - 4) {
                                                delay = 50 + Math.random() * 100; // 50-150ms au début/fin
                                            } else {
                                                delay = 20 + Math.random() * 60;  // 20-80ms au milieu
                                            }

                                            setTimeout(() => {
                                                slider.dispatchEvent(event);
                                            }, index * delay);
                                        });

                                        // Simuler le relâchement de la souris
                                        setTimeout(() => {
                                            const mouseUp = new MouseEvent('mouseup', {
                                                bubbles: true,
                                                cancelable: true,
                                                view: window,
                                                clientX: endX,
                                                clientY: endY
                                            });

                                            slider.dispatchEvent(mouseUp);
                                        }, steps * 50 + 100);

                                        return true;
                                    }
                                """, start_x, start_y, end_x, end_y)

                            # Méthode 3: Cliquer directement aux coordonnées retournées par 2Captcha
                            else:
                                self.logger.info("Méthode 3: Clic direct aux coordonnées retournées par 2Captcha dans la page principale")
                                await page.mouse.click(adjusted_x, adjusted_y)

                        except Exception as e:
                            self.logger.warning(f"Erreur lors de l'interaction avec la page principale: {e}")
                            # Fallback: cliquer directement aux coordonnées
                            self.logger.info("Fallback: Clic direct aux coordonnées ajustées")
                            await page.mouse.click(adjusted_x, adjusted_y)

                    else:
                        # Stratégie 3: Utiliser les coordonnées retournées par 2Captcha
                        self.logger.info(f"Stratégie 3 (tentative {click_attempt}): Clic direct aux coordonnées")

                        # Essayer d'abord dans l'iframe si disponible
                        if frame:
                            try:
                                self.logger.info("Tentative de clic dans l'iframe aux coordonnées retournées par 2Captcha")
                                await frame.mouse.click(target_x, target_y)
                            except Exception as e:
                                self.logger.warning(f"Erreur lors du clic dans l'iframe: {e}")
                                # Fallback: cliquer dans la page principale
                                self.logger.info("Fallback: Clic dans la page principale aux coordonnées ajustées")
                                await page.mouse.click(adjusted_x, adjusted_y)
                        else:
                            # Pas d'iframe, cliquer dans la page principale
                            self.logger.info("Clic dans la page principale aux coordonnées ajustées")
                            await page.mouse.click(adjusted_x, adjusted_y)

                    # Attendre pour voir si le captcha est résolu
                    # Attendre plus longtemps pour les méthodes JavaScript qui utilisent setTimeout
                    if click_attempt % 3 == 2:  # Méthode JavaScript
                        await page.wait_for_timeout(8000)  # Attendre plus longtemps pour les méthodes JavaScript
                    else:
                        await page.wait_for_timeout(5000)  # Attente standard

                    # Vérifier si le captcha a disparu
                    captcha_still_visible = False

                    # Vérifier d'abord les iframes captcha
                    try:
                        if await page.is_visible("iframe[src*='captcha']", timeout=1000):
                            captcha_still_visible = True
                            self.logger.warning("L'iframe captcha est toujours présent")
                    except Exception as e:
                        self.logger.info(f"Erreur lors de la vérification de l'iframe captcha: {e}")
                        # Si une erreur se produit, c'est peut-être parce que la page a changé
                        # On considère que le captcha a disparu

                    # Vérifier aussi les autres indicateurs de captcha
                    verification_selectors = [
                        "div:has-text('Verification Required')",
                        "div:has-text('Slide right to complete')",
                        "div:has-text('Slide right')",
                        "div:has-text('puzzle')",
                        "div.slider-captcha",
                        ".slider-puzzle",
                        "button.slider-btn",
                        ".captcha-slider",
                        ".slider"
                    ]

                    for selector in verification_selectors:
                        try:
                            if await page.is_visible(selector, timeout=1000):
                                captcha_still_visible = True
                                self.logger.warning(f"Le captcha est toujours présent après la solution avec le sélecteur: {selector}")
                                break
                        except Exception:
                            continue

                    # Vérifier si l'URL a changé (indiquant une redirection après connexion réussie)
                    current_url = page.url
                    initial_url = current_url

                    # Attendre un peu plus pour voir si la page change
                    await page.wait_for_timeout(3000)

                    # Prendre une capture d'écran après la tentative de résolution
                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    await page.screenshot(path=f"screenshots/after_captcha_attempt_{click_attempt}_{timestamp}.png")

                    if page.url != initial_url:
                        self.logger.info(f"URL changée de {initial_url} à {page.url}, captcha probablement résolu")

                        # Vérifier si nous sommes sur la page de connexion
                        if "login" in page.url:
                            self.logger.info("Redirection vers la page de connexion détectée après résolution du captcha")
                            # La résolution du captcha a fonctionné, mais nous sommes redirigés vers la page de connexion
                            # Nous considérons que le captcha est résolu, la connexion sera gérée par la méthode appelante

                        return True

                    # Vérifier si le contenu de la page a changé (même si l'URL n'a pas changé)
                    # Par exemple, vérifier si des éléments de la page de connexion sont apparus
                    login_elements = [
                        "input[type='password']",
                        "button:has-text('Me connecter')",
                        "button:has-text('Connexion')",
                        "form.login-form",
                        "input[name='email']",
                        "input[type='email']",
                        "input[id*='email']",
                        "input[id*='username']",
                        "input[id*='login']"
                    ]

                    login_page_detected = False
                    for selector in login_elements:
                        try:
                            if await page.is_visible(selector, timeout=1000):
                                login_page_detected = True
                                self.logger.info(f"Page de connexion détectée après résolution du captcha avec le sélecteur: {selector}")
                                break
                        except Exception:
                            continue

                    if login_page_detected:
                        self.logger.info("Captcha résolu avec succès, page de connexion détectée!")
                        return True

                    # Vérifier si le captcha a disparu
                    if not captcha_still_visible:
                        # Double vérification pour s'assurer que le captcha a bien disparu
                        captcha_still_present = False

                        # Vérifier à nouveau les iframes captcha
                        try:
                            if await page.is_visible("iframe[src*='captcha']", timeout=1000):
                                captcha_still_present = True
                                self.logger.warning("L'iframe captcha est toujours présent lors de la double vérification")
                        except Exception:
                            pass

                        # Vérifier à nouveau les autres indicateurs de captcha
                        for selector in verification_selectors:
                            try:
                                if await page.is_visible(selector, timeout=1000):
                                    captcha_still_present = True
                                    self.logger.warning(f"Le captcha est toujours présent lors de la double vérification avec le sélecteur: {selector}")
                                    break
                            except Exception:
                                continue

                        if not captcha_still_present:
                            self.logger.info("Double vérification confirmée: Captcha résolu avec succès!")
                            return True
                        else:
                            self.logger.warning("Le captcha semblait avoir disparu mais est toujours présent lors de la double vérification")
                            captcha_still_visible = True

                    # Si le captcha est toujours visible et que nous n'avons pas atteint le nombre maximum de tentatives
                    if captcha_still_visible:
                        if click_attempt < max_click_attempts:
                            self.logger.warning(f"Tentative {click_attempt} non réussie, essai suivant...")
                            # Attendre un peu avant la prochaine tentative
                            await page.wait_for_timeout(2000)
                            continue
                        else:
                            # Dernière tentative: essayer de cliquer sur un bouton "Je ne suis pas un robot"
                            self.logger.warning("Tentatives standard non réussies, essai avec des sélecteurs spécifiques...")

                            # Essayer de cliquer sur un bouton "Je ne suis pas un robot" ou similaire
                            robot_selectors = [
                                "#recaptcha-anchor",
                                ".recaptcha-checkbox-border",
                                "[role='checkbox']",
                                "button:has-text('Je ne suis pas un robot')",
                                "button:has-text('I am not a robot')",
                                "div.recaptcha-checkbox-checkmark"
                            ]

                            clicked = False
                            for selector in robot_selectors:
                                try:
                                    if frame and await frame.is_visible(selector, timeout=1000):
                                        await frame.click(selector)
                                        clicked = True
                                        self.logger.info(f"Clic sur {selector} dans l'iframe")
                                        break
                                    elif await page.is_visible(selector, timeout=1000):
                                        await page.click(selector)
                                        clicked = True
                                        self.logger.info(f"Clic sur {selector} dans la page principale")
                                        break
                                except Exception as e:
                                    self.logger.debug(f"Erreur lors du clic sur {selector}: {e}")

                            if clicked:
                                # Attendre pour voir si le captcha est résolu après le clic spécifique
                                await page.wait_for_timeout(5000)

                                # Vérifier à nouveau si le captcha a disparu
                                try:
                                    captcha_still_visible = await page.is_visible("iframe[src*='captcha']", timeout=1000)
                                except Exception:
                                    captcha_still_visible = False

                                if not captcha_still_visible:
                                    self.logger.info("Captcha résolu avec succès après le clic spécifique!")
                                    return True

                            self.logger.warning("La résolution du captcha n'a pas fonctionné après plusieurs tentatives")
                            return False
                    else:
                        self.logger.info("Captcha résolu avec succès!")
                        return True

                except Exception as e:
                    self.logger.error(f"Erreur lors de la tentative de clic {click_attempt}: {e}")
                    if click_attempt < max_click_attempts:
                        self.logger.info(f"Nouvelle tentative de clic ({click_attempt+1}/{max_click_attempts})...")
                        await page.wait_for_timeout(2000)  # Attendre un peu avant de réessayer
                        continue
                    return False

        except Exception as e:
            self.logger.error(f"Erreur générale lors de la résolution avec 2Captcha: {e}")
            return False

    async def _solve_slider_captcha(self, page: Page) -> bool:
        """Tente de résoudre un captcha slider avec plusieurs tentatives"""
        try:
            self.logger.info("Recherche du slider...")

            # Simuler un comportement humain sur la page avant de résoudre le captcha
            await self.simulate_human_behavior(page)

            # Capture d'écran avant de commencer
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            await self.screenshot_with_timestamp(page, f"screenshots/before_slider_{timestamp}.png")

            # Nombre maximum de tentatives pour résoudre le captcha
            max_captcha_attempts = 3  # Réduire le nombre de tentatives pour éviter les boucles infinies
            captcha_attempt = 1
            captcha_solved = False

            # Ajouter un timeout global pour éviter une boucle infinie
            start_time = time.time()
            max_time_seconds = 120  # Maximum 2 minutes pour résoudre le captcha

            # Boucle de tentatives pour résoudre le captcha
            while captcha_attempt <= max_captcha_attempts and not captcha_solved:
                # Vérifier si nous avons dépassé le temps maximum
                if time.time() - start_time > max_time_seconds:
                    self.logger.warning(f"Timeout dépassé après {max_time_seconds} secondes. Abandon de la résolution du captcha.")
                    break

                self.logger.info(f"Tentative de résolution du captcha {captcha_attempt}/{max_captcha_attempts}...")

                # Essayer d'abord avec 2Captcha si configuré
                if self.config.get("auth", {}).get("captcha", {}).get("api_key"):
                    self.logger.info(f"Tentative {captcha_attempt}: Résolution avec 2Captcha...")

                    # Prendre une nouvelle capture d'écran à chaque tentative
                    new_timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    await self.screenshot_with_timestamp(page, f"screenshots/captcha_attempt_{captcha_attempt}_{new_timestamp}.png")

                    # Limiter le temps d'attente pour la résolution avec 2Captcha
                    try:
                        captcha_solved = await asyncio.wait_for(
                            self._solve_captcha_with_2captcha(page),
                            timeout=60  # Maximum 60 secondes par tentative
                        )
                    except asyncio.TimeoutError:
                        self.logger.warning("Timeout lors de la résolution avec 2Captcha")
                        captcha_solved = False

                    if captcha_solved:
                        self.logger.info(f"Captcha résolu avec succès via 2Captcha à la tentative {captcha_attempt}!")

                        # Vérifier si nous sommes redirigés vers la page de connexion
                        await page.wait_for_timeout(2000)  # Attendre que la page se stabilise

                        current_url = page.url
                        self.logger.info(f"URL après résolution du captcha: {current_url}")

                        # Vérifier si nous sommes sur la page de connexion
                        if "login" in current_url:
                            self.logger.info("Redirection vers la page de connexion détectée après résolution du captcha")

                            # Vérifier si le formulaire de connexion est visible
                            login_form_visible = False
                            login_form_selectors = [
                                "input[type='password']",
                                "button:has-text('Me connecter')",
                                "button:has-text('Connexion')",
                                "form.login-form",
                                "input[name='email']",
                                "input[type='email']",
                                "input[id*='email']",
                                "input[id*='username']",
                                "input[id*='login']",
                                "input:first-of-type"
                            ]

                            for selector in login_form_selectors:
                                try:
                                    if await page.is_visible(selector, timeout=1000):
                                        login_form_visible = True
                                        self.logger.info(f"Formulaire de connexion détecté avec le sélecteur: {selector}")
                                        break
                                except Exception:
                                    continue

                            if login_form_visible:
                                self.logger.info("Remplissage du formulaire de connexion après résolution du captcha...")

                                # Simuler un comportement humain sur la page avant de remplir le formulaire
                                await self.simulate_human_behavior(page)

                                # Capture d'écran avant de remplir le formulaire
                                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                                await self.screenshot_with_timestamp(page, f"screenshots/before_post_captcha_login_{timestamp}.png")

                                # Remplir le formulaire de connexion
                                email_selectors = [
                                    "input[aria-label='Adresse Email ou identifiant client']",
                                    "input[placeholder='Adresse Email ou identifiant client']",
                                    "input[name='email']",
                                    "input[type='email']",
                                    "input[id*='email']",
                                    "input[id*='username']",
                                    "input[id*='login']",
                                    "input:first-of-type"
                                ]

                                password_selectors = [
                                    "input[aria-label='Mot de passe']",
                                    "input[placeholder='Mot de passe']",
                                    "input[name='password']",
                                    "input[type='password']",
                                    "input[id*='password']",
                                    "input[id*='pwd']",
                                    "input:nth-of-type(2)"
                                ]

                                login_button_selectors = [
                                    "button:has-text('Me connecter')",
                                    "button.btn-primary",
                                    "button.btn-connect",
                                    "button.login-button",
                                    "button[type='submit']",
                                    "input[type='submit']",
                                    ".btn-connect",
                                    ".btn-primary",
                                    "button.submit-button",
                                    "button:has-text('Connexion')",
                                    "button"
                                ]

                                # Trouver le champ email
                                email_field = None
                                for selector in email_selectors:
                                    try:
                                        if await page.is_visible(selector, timeout=1000):
                                            email_field = await page.query_selector(selector)
                                            self.logger.info(f"Champ email trouvé avec le sélecteur: {selector}")
                                            break
                                    except Exception as e:
                                        self.logger.debug(f"Erreur lors de la recherche du champ email avec le sélecteur {selector}: {e}")
                                        continue

                                # Trouver le champ mot de passe
                                password_field = None
                                for selector in password_selectors:
                                    try:
                                        if await page.is_visible(selector, timeout=1000):
                                            password_field = await page.query_selector(selector)
                                            self.logger.info(f"Champ mot de passe trouvé avec le sélecteur: {selector}")
                                            break
                                    except Exception as e:
                                        self.logger.debug(f"Erreur lors de la recherche du champ mot de passe avec le sélecteur {selector}: {e}")
                                        continue

                                # Remplir le champ email avec un comportement humain
                                email_filled = False
                                if email_field:
                                    try:
                                        # Vider le champ d'abord
                                        await email_field.click(click_count=3)  # Triple-clic pour sélectionner tout le texte
                                        await email_field.press("Backspace")  # Effacer le texte sélectionné

                                        # Simuler une frappe humaine très réaliste avec des délais variables entre les caractères
                                        username = self.config["auth"]["username"]

                                        # Pause initiale avant de commencer à taper (comme si on réfléchissait)
                                        await page.wait_for_timeout(random.randint(500, 1200))

                                        # Définir différents groupes de vitesse de frappe pour simuler un comportement plus naturel
                                        typing_speeds = []

                                        # Diviser le nom d'utilisateur en groupes de 3-5 caractères
                                        chars_per_group = random.randint(3, 5)
                                        for i in range(0, len(username), chars_per_group):
                                            # Vitesse de frappe pour ce groupe (ms entre les caractères)
                                            group_speed = random.randint(70, 250)
                                            typing_speeds.extend([group_speed] * min(chars_per_group, len(username) - i))

                                        for i, char in enumerate(username):
                                            # Obtenir le délai pour ce caractère
                                            delay = typing_speeds[i]

                                            # Ajuster le délai en fonction de la position (plus lent au début et à la fin)
                                            if i < 2:
                                                delay += random.randint(50, 150)  # Plus lent au début
                                            elif i > len(username) - 3:
                                                delay += random.randint(30, 100)  # Plus lent à la fin

                                            # Ajouter une variation aléatoire pour chaque caractère
                                            delay += random.randint(-20, 20)
                                            delay = max(50, delay)  # Assurer un délai minimum

                                            # Occasionnellement faire une pause plus longue (comme si on vérifiait ce qu'on a tapé)
                                            if random.random() < 0.08:  # 8% de chance
                                                pause_duration = random.randint(400, 1200)
                                                await page.wait_for_timeout(pause_duration)

                                            # Taper le caractère correct
                                            await email_field.type(char, delay=delay)

                                        email_filled = True
                                        self.logger.info("Champ email rempli avec un comportement humain")
                                    except Exception as e:
                                        self.logger.warning(f"Erreur lors du remplissage du champ email avec un comportement humain: {e}")
                                        # Fallback à la méthode simple
                                        try:
                                            await email_field.fill(self.config["auth"]["username"])
                                            email_filled = True
                                            self.logger.info("Champ email rempli avec la méthode simple (fallback)")
                                        except Exception as e2:
                                            self.logger.error(f"Erreur lors du remplissage du champ email avec la méthode simple: {e2}")

                                if not email_filled:
                                    self.logger.warning("Impossible de remplir le champ email")

                                # Pause entre le remplissage des deux champs
                                await page.wait_for_timeout(random.randint(500, 1500))

                                # Remplir le champ mot de passe avec un comportement humain
                                password_filled = False
                                if password_field:
                                    try:
                                        # Vider le champ d'abord
                                        await password_field.click(click_count=3)
                                        await password_field.press("Backspace")

                                        # Simuler une frappe humaine très réaliste pour le mot de passe
                                        password = self.config["auth"]["password"]

                                        # Pause avant de commencer à taper le mot de passe (comme si on réfléchissait)
                                        await page.wait_for_timeout(random.randint(700, 1500))

                                        # Définir différents groupes de vitesse de frappe pour simuler un comportement plus naturel
                                        typing_speeds = []

                                        # Diviser le mot de passe en groupes de 2-4 caractères (les mots de passe sont souvent tapés plus lentement)
                                        chars_per_group = random.randint(2, 4)
                                        for i in range(0, len(password), chars_per_group):
                                            # Vitesse de frappe pour ce groupe (ms entre les caractères)
                                            # Les mots de passe sont généralement tapés plus lentement
                                            group_speed = random.randint(100, 300)
                                            typing_speeds.extend([group_speed] * min(chars_per_group, len(password) - i))

                                        for i, char in enumerate(password):
                                            # Obtenir le délai pour ce caractère
                                            delay = typing_speeds[i]

                                            # Ajuster le délai en fonction de la position (plus lent au début et à la fin)
                                            if i < 2:
                                                delay += random.randint(70, 200)  # Plus lent au début
                                            elif i > len(password) - 3:
                                                delay += random.randint(50, 150)  # Plus lent à la fin

                                            # Ajouter une variation aléatoire pour chaque caractère
                                            delay += random.randint(-30, 30)
                                            delay = max(70, delay)  # Assurer un délai minimum plus élevé pour les mots de passe

                                            # Occasionnellement faire une pause plus longue (comme si on vérifiait ce qu'on a tapé)
                                            if random.random() < 0.12:  # 12% de chance (plus élevée pour les mots de passe)
                                                pause_duration = random.randint(500, 1500)
                                                await page.wait_for_timeout(pause_duration)

                                            # Taper le caractère
                                            await password_field.type(char, delay=delay)

                                        password_filled = True
                                        self.logger.info("Champ mot de passe rempli avec un comportement humain")
                                    except Exception as e:
                                        self.logger.warning(f"Erreur lors du remplissage du champ mot de passe avec un comportement humain: {e}")
                                        # Fallback à la méthode simple
                                        try:
                                            await password_field.fill(self.config["auth"]["password"])
                                            password_filled = True
                                            self.logger.info("Champ mot de passe rempli avec la méthode simple (fallback)")
                                        except Exception as e2:
                                            self.logger.error(f"Erreur lors du remplissage du champ mot de passe avec la méthode simple: {e2}")

                                if not password_filled:
                                    self.logger.warning("Impossible de remplir le champ mot de passe")

                                # Pause finale avant de cliquer sur le bouton
                                await page.wait_for_timeout(random.randint(800, 2000))

                                # Cliquer sur le bouton de connexion avec un comportement humain
                                button_clicked = False
                                for selector in login_button_selectors:
                                    try:
                                        if await page.is_visible(selector, timeout=1000):
                                            # Trouver le bouton
                                            button = await page.query_selector(selector)
                                            if button:
                                                # Obtenir la position du bouton
                                                box = await button.bounding_box()
                                                if box:
                                                    # Calculer le centre du bouton avec une légère variation aléatoire
                                                    center_x = box["x"] + box["width"] / 2 + random.randint(-10, 10)
                                                    center_y = box["y"] + box["height"] / 2 + random.randint(-5, 5)

                                                    # Obtenir la position actuelle de la souris
                                                    current_position = await page.evaluate("""
                                                        () => {
                                                            return {
                                                                x: window.mouseX || window.innerWidth / 2,
                                                                y: window.mouseY || window.innerHeight / 2
                                                            };
                                                        }
                                                    """)

                                                    # Déplacer la souris vers le bouton avec un mouvement naturel
                                                    await page.mouse.move(
                                                        current_position["x"],
                                                        current_position["y"],
                                                        steps=1
                                                    )

                                                    # Mouvement de souris avec une courbe naturelle
                                                    steps = random.randint(10, 20)
                                                    for i in range(1, steps + 1):
                                                        # Fonction d'accélération/décélération (easeInOutQuad)
                                                        t = i / steps
                                                        factor = 2 * t * t if t < 0.5 else 1 - math.pow(-2 * t + 2, 2) / 2

                                                        # Calculer la position intermédiaire avec une légère déviation aléatoire
                                                        deviation_x = random.randint(-10, 10) * math.sin(t * math.pi)
                                                        deviation_y = random.randint(-8, 8) * math.sin(t * math.pi)

                                                        pos_x = current_position["x"] + ((center_x - current_position["x"]) * factor) + deviation_x
                                                        pos_y = current_position["y"] + ((center_y - current_position["y"]) * factor) + deviation_y

                                                        # Déplacer la souris avec une pause variable
                                                        await page.mouse.move(pos_x, pos_y, steps=1)

                                                        # Pause plus longue au début et à la fin du mouvement
                                                        if i < 3 or i > steps - 3:
                                                            await page.wait_for_timeout(random.randint(30, 80))
                                                        else:
                                                            await page.wait_for_timeout(random.randint(10, 30))

                                                    # Pause finale avant le clic
                                                    await page.wait_for_timeout(random.randint(100, 300))

                                                    # Clic sur le bouton
                                                    await page.mouse.click(center_x, center_y)
                                                    self.logger.info(f"Bouton de connexion cliqué avec un comportement humain: {selector}")
                                                    button_clicked = True
                                                    break

                                            # Fallback si le code ci-dessus échoue
                                            if not button_clicked:
                                                await page.click(selector)
                                                self.logger.info(f"Bouton de connexion cliqué avec la méthode simple: {selector}")
                                                button_clicked = True
                                                break
                                    except Exception as e:
                                        self.logger.debug(f"Erreur lors du clic sur le bouton avec le sélecteur {selector}: {e}")
                                        continue

                                if not button_clicked:
                                    self.logger.warning("Impossible de cliquer sur le bouton de connexion")

                                    # Essayer de soumettre le formulaire directement
                                    try:
                                        await page.evaluate("""
                                            () => {
                                                const form = document.querySelector('form');
                                                if (form) {
                                                    console.log('Formulaire trouvé, soumission...');
                                                    form.submit();
                                                    return true;
                                                }
                                                return false;
                                            }
                                        """)
                                        self.logger.info("Formulaire soumis directement via JavaScript")
                                        button_clicked = True
                                    except Exception as e:
                                        self.logger.error(f"Erreur lors de la soumission du formulaire via JavaScript: {e}")

                                # Attendre que la page se charge après la connexion
                                if button_clicked:
                                    try:
                                        await page.wait_for_load_state("networkidle", timeout=10000)
                                        self.logger.info("Page chargée après la connexion post-captcha")

                                        # Vérifier si la connexion a réussi
                                        current_url = page.url
                                        if "login" not in current_url:
                                            self.logger.info(f"Connexion réussie après captcha! Nouvelle URL: {current_url}")
                                        else:
                                            self.logger.warning(f"Toujours sur la page de connexion après tentative: {current_url}")

                                            # Vérifier s'il y a un nouveau captcha
                                            captcha_visible = await page.is_visible("iframe[src*='captcha']", timeout=1000)
                                            if captcha_visible:
                                                self.logger.warning("Nouveau captcha détecté après tentative de connexion")
                                                # On considère quand même que le premier captcha a été résolu
                                            else:
                                                self.logger.info("Pas de nouveau captcha, la connexion est peut-être en cours...")
                                    except Exception as e:
                                        self.logger.warning(f"Erreur lors de l'attente du chargement de la page: {e}")
                        else:
                            self.logger.info("Pas de redirection vers la page de connexion après résolution du captcha")

                        return True
                    else:
                        self.logger.warning(f"Échec de la résolution avec 2Captcha à la tentative {captcha_attempt}")

                        # Si ce n'est pas la dernière tentative, vérifier si le captcha a expiré
                        if captcha_attempt < max_captcha_attempts:
                            # Vérifier si le captcha a expiré
                            captcha_expired = False
                            refresh_button_selectors = [
                                "button.refresh-button",
                                "button.captcha-refresh",
                                ".refresh-icon",
                                "button:has-text('Réessayer')",
                                "button:has-text('RÉESSAYER')",
                                "img[alt='refresh']",
                                ".captcha-refresh-icon"
                            ]

                            # Prendre une capture d'écran pour vérifier visuellement l'état du captcha
                            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                            await self.screenshot_with_timestamp(page, f"screenshots/captcha_check_outer_{timestamp}.png")

                            # Vérifier si un bouton de rafraîchissement est visible
                            for selector in refresh_button_selectors:
                                try:
                                    if await page.is_visible(selector, timeout=1000):
                                        captcha_expired = True
                                        self.logger.info(f"Bouton de rafraîchissement du captcha détecté avec le sélecteur: {selector}")
                                        # Cliquer sur le bouton de rafraîchissement
                                        await page.click(selector)
                                        self.logger.info("Clic sur le bouton de rafraîchissement du captcha")
                                        await page.wait_for_timeout(2000)  # Attendre que le nouveau captcha se charge
                                        break
                                except Exception:
                                    continue

                            # Si le captcha a expiré mais qu'aucun bouton n'a été trouvé, essayer de cliquer sur l'image du captcha
                            if not captcha_expired:
                                # Vérifier si l'image du captcha contient une icône de rafraîchissement (comme dans l'image fournie)
                                try:
                                    # Prendre une capture d'écran du captcha pour analyse
                                    captcha_image_selectors = [
                                        "img[src*='captcha']",
                                        "iframe[src*='captcha']",
                                        ".captcha-image",
                                        ".captcha-container img"
                                    ]

                                    for selector in captcha_image_selectors:
                                        try:
                                            if await page.is_visible(selector, timeout=1000):
                                                # Cliquer au centre de l'image du captcha pour le rafraîchir
                                                element = await page.query_selector(selector)
                                                if element:
                                                    box = await element.bounding_box()
                                                    if box:
                                                        center_x = box["x"] + box["width"] / 2
                                                        center_y = box["y"] + box["height"] / 2
                                                        await page.mouse.click(center_x, center_y)
                                                        self.logger.info("Clic au centre de l'image du captcha pour le rafraîchir")
                                                        captcha_expired = True
                                                        await page.wait_for_timeout(2000)  # Attendre que le nouveau captcha se charge

                                                        # Prendre une capture d'écran après le rafraîchissement
                                                        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                                                        await page.screenshot(path=f"screenshots/after_captcha_refresh_{timestamp}.png")

                                                        # Si le captcha a été rafraîchi, sortir de la boucle de vérification
                                                        if captcha_expired:
                                                            self.logger.info("Captcha rafraîchi avec succès, sortie de la boucle de vérification")
                                                            break
                                        except Exception as e:
                                            self.logger.debug(f"Erreur lors du clic sur l'image du captcha {selector}: {e}")
                                            continue
                                except Exception as e:
                                    self.logger.debug(f"Erreur lors de la tentative de clic sur l'image du captcha: {e}")

                            if captcha_expired:
                                self.logger.info("Le captcha a été rafraîchi, passage à la prochaine tentative immédiatement")
                                # Prendre une nouvelle capture d'écran après le rafraîchissement
                                await page.screenshot(path=f"screenshots/captcha_after_refresh_outer_{timestamp}.png")
                            else:
                                # Si le captcha n'a pas expiré, attendre environ 1 minute pour que le captcha change naturellement
                                wait_time = 60 + random.randint(-5, 5)  # 55-65 secondes pour simuler environ 1 minute avec variation
                                self.logger.info(f"Captcha non expiré. Attente de {wait_time} secondes avant la prochaine tentative pour permettre au captcha de changer...")
                                await page.wait_for_timeout(wait_time * 1000)  # Convertir en millisecondes

                            # Vérifier si le captcha est toujours présent
                            captcha_still_visible = await page.is_visible("iframe[src*='captcha']", timeout=1000)
                            if not captcha_still_visible:
                                self.logger.info("Le captcha a disparu pendant l'attente, vérification si nous sommes connectés...")
                                # Vérifier si nous sommes sur la page de connexion
                                if "login" not in page.url:
                                    self.logger.info("Nous ne sommes plus sur la page de connexion, captcha probablement résolu!")
                                    return True

                        captcha_attempt += 1
                else:
                    self.logger.warning("Clé API 2Captcha non configurée, tentative avec les méthodes internes...")
                    break  # Sortir de la boucle et continuer avec les méthodes internes

            # Si nous avons épuisé toutes les tentatives avec 2Captcha sans succès
            if not captcha_solved and self.config.get("auth", {}).get("captcha", {}).get("api_key"):
                self.logger.warning(f"Échec de la résolution avec 2Captcha après {max_captcha_attempts} tentatives, passage aux méthodes internes...")

            # Recherche du bouton slider
            slider_selectors = [
                "button.slider-btn",
                ".slider-button",
                ".slider-handle",
                "div.slider",
                "div[role='slider']",
                "div.sliderContainer",
                "div.slider-container",
                "div.slider-btn",
                "div.sliderBtn",
                "div.captcha-slider",
                "div.puzzle-slider-button"
            ]

            slider_element = None
            for selector in slider_selectors:
                try:
                    if await page.is_visible(selector, timeout=1000):
                        slider_element = await page.query_selector(selector)
                        self.logger.info(f"Slider trouvé avec le sélecteur: {selector}")
                        break
                except Exception:
                    continue

            if not slider_element:
                self.logger.error("Impossible de trouver le slider")
                return False

            # Obtenir les dimensions et la position du slider
            slider_box = await slider_element.bounding_box()
            if not slider_box:
                self.logger.error("Impossible d'obtenir les dimensions du slider")
                return False

            # Obtenir les dimensions du conteneur du slider
            container_selectors = [
                ".slider-container",
                ".sliderContainer",
                ".captcha-slider",
                ".puzzle-slider",
                ".slider-track",
                ".slider-rail"
            ]

            container_width = None
            for selector in container_selectors:
                try:
                    container = await page.query_selector(selector)
                    if container:
                        container_box = await container.bounding_box()
                        if container_box:
                            container_width = container_box["width"]
                            self.logger.info(f"Largeur du conteneur: {container_width}px")
                            break
                except Exception:
                    continue

            # Si on n'a pas trouvé le conteneur, utiliser une valeur par défaut
            if not container_width:
                # Essayer de trouver la largeur en utilisant JavaScript
                try:
                    container_width = await page.evaluate("""
                        () => {
                            const sliders = document.querySelectorAll('.slider-container, .sliderContainer, .captcha-slider, .puzzle-slider, .slider-track, .slider-rail');
                            if (sliders.length > 0) {
                                return sliders[0].offsetWidth;
                            }
                            return 300; // Valeur par défaut
                        }
                    """)
                    self.logger.info(f"Largeur du conteneur (via JS): {container_width}px")
                except Exception:
                    container_width = 300  # Valeur par défaut
                    self.logger.info(f"Utilisation d'une largeur par défaut: {container_width}px")

            # Calculer la distance à faire glisser (environ 90% de la largeur disponible)
            start_x = slider_box["x"] + slider_box["width"] / 2
            start_y = slider_box["y"] + slider_box["height"] / 2

            # Calculer la distance à faire glisser
            distance = container_width * 0.9 if container_width else 200

            # Faire glisser le slider
            self.logger.info(f"Déplacement du slider sur {distance}px")

            # Méthode 1: Utiliser mouse.down, mouse.move, mouse.up
            try:
                await page.mouse.move(start_x, start_y)
                await page.mouse.down()

                # Déplacer progressivement pour simuler un mouvement humain
                steps = 10
                for i in range(1, steps + 1):
                    await page.mouse.move(
                        start_x + (distance * i / steps),
                        start_y + random.uniform(-2, 2),  # Légère variation verticale
                        steps=5  # Mouvement plus fluide
                    )
                    await page.wait_for_timeout(random.randint(50, 150))  # Pause aléatoire

                await page.mouse.up()

                # Attendre un peu pour voir si le captcha est résolu
                await page.wait_for_timeout(2000)

                # Capture d'écran après le glissement
                await page.screenshot(path=f"screenshots/after_slider_{timestamp}.png")

                # Vérifier si le captcha a disparu
                verification_selectors = [
                    "div:has-text('Verification Required')",
                    "div:has-text('Slide right to complete')",
                    "div:has-text('Slide right')",
                    "div:has-text('puzzle')",
                    "div.slider-captcha",
                    ".slider-puzzle",
                    "button.slider-btn"
                ]

                # Variable pour stocker les sélecteurs pour une utilisation ultérieure
                self.verification_selectors = verification_selectors

                captcha_still_visible = False
                for selector in verification_selectors:
                    try:
                        if await page.is_visible(selector, timeout=1000):
                            captcha_still_visible = True
                            self.logger.warning(f"Le captcha est toujours présent après le glissement avec le sélecteur: {selector}")
                            break
                    except Exception:
                        continue

                if captcha_still_visible:
                    # Essayer une deuxième méthode si la première a échoué
                    self.logger.info("Tentative avec une méthode alternative...")

                    # Méthode 2: Utiliser drag_and_drop
                    try:
                        # Trouver à nouveau le slider (il pourrait avoir changé)
                        for selector in slider_selectors:
                            try:
                                slider_element = await page.query_selector(selector)
                                if slider_element:
                                    break
                            except Exception:
                                continue

                        if slider_element:
                            # Calculer la position cible (pour le log uniquement)
                            slider_box = await slider_element.bounding_box()

                            # Effectuer le drag and drop
                            try:
                                # Méthode 1: Utiliser drag_and_drop si disponible
                                await page.drag_and_drop(
                                    f"text=Slide right",
                                    f"div.slider-container >> nth=0",
                                    target_position={"x": distance, "y": 0}
                                )
                            except Exception:
                                # Méthode 2: Utiliser JavaScript pour faire glisser le slider
                                await page.evaluate("""
                                    (distance) => {
                                        const slider = document.querySelector('.slider-btn, .slider-button, .slider-handle, [role="slider"]');
                                        if (!slider) return false;

                                        // Simuler un événement de glissement
                                        const rect = slider.getBoundingClientRect();
                                        const startX = rect.left + rect.width / 2;
                                        const startY = rect.top + rect.height / 2;

                                        // Créer les événements
                                        const mouseDown = new MouseEvent('mousedown', {
                                            bubbles: true,
                                            cancelable: true,
                                            view: window,
                                            clientX: startX,
                                            clientY: startY
                                        });

                                        const mouseMove = new MouseEvent('mousemove', {
                                            bubbles: true,
                                            cancelable: true,
                                            view: window,
                                            clientX: startX + distance,
                                            clientY: startY
                                        });

                                        const mouseUp = new MouseEvent('mouseup', {
                                            bubbles: true,
                                            cancelable: true,
                                            view: window,
                                            clientX: startX + distance,
                                            clientY: startY
                                        });

                                        // Déclencher les événements
                                        slider.dispatchEvent(mouseDown);
                                        setTimeout(() => {
                                            slider.dispatchEvent(mouseMove);
                                            setTimeout(() => {
                                                slider.dispatchEvent(mouseUp);
                                            }, 100);
                                        }, 100);

                                        return true;
                                    }
                                """, distance)

                            # Attendre un peu
                            await page.wait_for_timeout(2000)

                            # Capture d'écran après la deuxième tentative
                            await page.screenshot(path=f"screenshots/after_slider_method2_{timestamp}.png")
                    except Exception as e:
                        self.logger.error(f"Erreur lors de la deuxième tentative: {e}")

                    # Vérifier à nouveau si le captcha a disparu
                    captcha_still_present = False
                    for selector in self.verification_selectors:
                        try:
                            if await page.is_visible(selector, timeout=1000):
                                captcha_still_present = True
                                self.logger.warning(f"Le captcha est toujours présent après la méthode alternative avec le sélecteur: {selector}")
                                break
                        except Exception:
                            continue

                    if captcha_still_present:
                        self.logger.error("Échec de la résolution du captcha après plusieurs tentatives")
                        return False
                    else:
                        self.logger.info("Captcha résolu avec la méthode alternative!")
                        return True

                # Si on arrive ici, c'est que le captcha a disparu
                self.logger.info("Captcha résolu avec succès!")
                return True

            except Exception as e:
                self.logger.error(f"Erreur lors de la résolution du captcha slider: {e}")
                return False

        except Exception as e:
            self.logger.error(f"Erreur générale lors de la résolution du captcha: {e}")
            return False

    async def _launch_browser(self, headless: bool = True) -> Tuple[Browser, BrowserContext]:
        """Lance le navigateur avec des options améliorées pour éviter la détection en tant que bot"""
        with tracer.start_as_current_span("launch_browser"):
            self.logger.info(f"Lancement du navigateur en mode {'headless' if headless else 'visible (non-headless)'}")

            # Obtenir la configuration proxy si disponible
            proxy_config = None
            if self.proxy_rotator:
                proxy_config = await self.proxy_rotator.get_current_proxy()
                if proxy_config:
                    self.logger.info(f"Utilisation du proxy: {proxy_config['country']} via {proxy_config['server']}")

            # Utiliser undetected-playwright si disponible
            if UNDETECTED_AVAILABLE:
                self.logger.info("Utilisation de undetected-playwright pour éviter la détection")
                try:
                    # Créer une instance de navigateur non détectable
                    browser = await undetected_playwright.stealth_async()

                    # Configuration du contexte avec proxy si disponible
                    context_options = {
                        "locale": "fr-FR",
                        "timezone_id": "Europe/Paris",
                        "geolocation": {"latitude": 48.8566, "longitude": 2.3522}, # Paris
                        "color_scheme": "no-preference",
                        "reduced_motion": "no-preference"
                    }

                    if proxy_config:
                        context_options["proxy"] = {
                            "server": proxy_config["server"],
                            "username": proxy_config["username"],
                            "password": proxy_config["password"]
                        }

                    context = await browser.new_context(**context_options)

                    # Configuration des timeouts avec une légère variation
                    timeout_base = self.config["browser"]["navigation_timeout"] * 1000
                    timeout_variation = random.randint(-1000, 1000)  # Variation de ±1 seconde
                    context.set_default_timeout(timeout_base + timeout_variation)
                    context.set_default_navigation_timeout(timeout_base + timeout_variation)

                    return browser, context
                except Exception as e:
                    self.logger.warning(f"Échec de l'initialisation avec undetected-playwright: {e}")
                    self.logger.info("Retour à la méthode standard avec améliorations")

            # Méthode standard améliorée
            playwright = await async_playwright().start()
            browser_type = getattr(playwright, self.config["browser"]["type"].lower())

            # Utiliser fake-useragent pour générer un agent utilisateur aléatoire réaliste
            try:
                ua = UserAgent()
                user_agent = ua.random
                self.logger.debug(f"Utilisation de l'agent utilisateur généré: {user_agent}")
            except Exception as e:
                self.logger.warning(f"Erreur lors de la génération d'un agent utilisateur aléatoire: {e}")
                # Liste de secours d'agents utilisateurs modernes et réalistes
                user_agents = [
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:123.0) Gecko/20100101 Firefox/123.0",
                    "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Safari/605.1.15",
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0"
                ]
                user_agent = random.choice(user_agents)
                self.logger.debug(f"Utilisation de l'agent utilisateur de secours: {user_agent}")

            # Dimensions d'écran réalistes et variées
            screen_sizes = [
                {"width": 1366, "height": 768},  # Très courant
                {"width": 1920, "height": 1080}, # Full HD
                {"width": 1440, "height": 900},  # MacBook
                {"width": 1536, "height": 864},  # Commun sur Windows
                {"width": 1280, "height": 800}   # Autre taille courante
            ]
            viewport = random.choice(screen_sizes)

            # Configuration du navigateur avec des paramètres plus réalistes et anti-détection
            browser_args = [
                '--disable-blink-features=AutomationControlled',
                '--disable-features=VizDisplayCompositor',
                '--disable-features=IsolateOrigins,site-per-process',
                f'--window-size={viewport["width"]},{viewport["height"]}',
                '--disable-extensions',
                '--disable-component-extensions-with-background-pages',
                '--disable-default-apps',
                '--no-default-browser-check',
                '--no-first-run',
                '--lang=fr-FR,fr',
                '--disable-translate',
                '--disable-infobars',
                '--disable-notifications',
                '--disable-popup-blocking',
                '--disable-save-password-bubble',
                '--disable-single-click-autofill',
                '--disable-autofill-keyboard-accessory-view',
                '--disable-site-isolation-trials',
                '--disable-features=TranslateUI,BlinkGenPropertyTrees',
                '--disable-hang-monitor',
                '--disable-ipc-flooding-protection',
                '--disable-renderer-backgrounding',
                '--disable-backgrounding-occluded-windows',
                '--disable-background-timer-throttling',
                '--disable-background-networking',
                '--disable-breakpad',
                '--disable-client-side-phishing-detection',
                '--disable-default-apps',
                '--disable-dev-shm-usage',
                '--disable-prompt-on-repost',
                '--metrics-recording-only',
                '--no-sandbox',
                '--no-zygote',
                '--password-store=basic',
                # Arguments anti-détection supplémentaires
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-features=VizServiceDisplayCompositor',
                '--disable-gpu-sandbox',
                '--disable-software-rasterizer',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-field-trial-config',
                '--disable-back-forward-cache',
                '--disable-features=ScriptStreaming',
                '--disable-features=V8OptimizeJavascript',
                '--disable-features=V8FlushBytecode',
                '--disable-features=V8FlushBaselineCode',

                '--aggressive-cache-discard',
                '--memory-pressure-off'
            ]

            # Ajout d'un délai aléatoire pour simuler un comportement humain (plus lent)
            slow_mo = random.randint(200, 500)  # Augmenté pour être plus humain

            # Configuration du navigateur
            browser = await browser_type.launch(
                headless=headless,
                slow_mo=slow_mo,
                timeout=self.config["browser"]["launch_timeout"] * 1000,
                args=browser_args
            )

            # Création d'un contexte avec des dimensions d'écran réalistes
            context_options = {
                "viewport": viewport,
                "user_agent": user_agent,
                "locale": "fr-FR",
                "timezone_id": "Europe/Paris",
                "geolocation": {"latitude": 48.8566, "longitude": 2.3522}, # Paris
                "color_scheme": "no-preference",
                "reduced_motion": "no-preference",
                "has_touch": random.choice([True, False]),  # Simuler aléatoirement un écran tactile
                "is_mobile": False,  # Ne pas simuler un appareil mobile
                "device_scale_factor": random.choice([1, 1.25, 1.5, 2])  # Simuler différentes densités d'écran
            }

            # Ajouter la configuration proxy si disponible
            if proxy_config:
                context_options["proxy"] = {
                    "server": proxy_config["server"],
                    "username": proxy_config["username"],
                    "password": proxy_config["password"]
                }

            context = await browser.new_context(**context_options)

            # Appliquer le module stealth si disponible
            if STEALTH_AVAILABLE:
                self.logger.info("Application du module playwright_stealth pour éviter la détection")
                try:
                    page = await context.new_page()
                    await playwright_stealth.stealth_async(page)
                    await page.close()
                except Exception as e:
                    self.logger.warning(f"Erreur lors de l'application du module stealth: {e}")

            # Ajout d'empreintes de navigateur supplémentaires via JavaScript (version améliorée)
            await context.add_init_script("""
                // Masquer TOUS les indicateurs d'automatisation
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });

                // Supprimer les propriétés de détection Playwright/Selenium
                delete window.navigator.__proto__.webdriver;
                delete window.navigator.webdriver;
                delete window.webdriver;
                delete window._phantom;
                delete window.__phantom;
                delete window.callPhantom;
                delete window.Buffer;
                delete window.emit;
                delete window.spawn;

                // Masquer les variables Playwright
                delete window.playwright;
                delete window.__playwright;
                delete window.__pw_manual;
                delete window.__PW_inspect;

                // Redéfinir chrome pour qu'il soit plus réaliste
                if (!window.chrome) {
                    window.chrome = {};
                }

                window.chrome.runtime = {
                    onConnect: undefined,
                    onMessage: undefined
                };

                // Simuler des plugins de navigateur plus réalistes
                Object.defineProperty(navigator, 'plugins', {
                    get: () => {
                        return [
                            {
                                0: {type: "application/pdf", suffixes: "pdf", description: "Portable Document Format"},
                                description: "Portable Document Format",
                                filename: "internal-pdf-viewer",
                                length: 1,
                                name: "Chrome PDF Plugin"
                            },
                            {
                                0: {type: "application/pdf", suffixes: "pdf", description: "Portable Document Format"},
                                description: "Portable Document Format",
                                filename: "internal-pdf-viewer",
                                length: 1,
                                name: "Chrome PDF Viewer"
                            },
                            {
                                0: {type: "application/x-google-chrome-pdf", suffixes: "pdf", description: "Portable Document Format"},
                                description: "Portable Document Format",
                                filename: "internal-pdf-viewer",
                                length: 1,
                                name: "PDF Viewer"
                            },
                            {
                                0: {type: "application/x-nacl", suffixes: "", description: "Native Client Executable"},
                                1: {type: "application/x-pnacl", suffixes: "", description: "Portable Native Client Executable"},
                                description: "Native Client",
                                filename: "internal-nacl-plugin",
                                length: 2,
                                name: "Native Client"
                            }
                        ];
                    },
                });

                // Simuler des langues plus réalistes
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['fr-FR', 'fr', 'en-US', 'en'],
                });

                // Simuler une plateforme réaliste
                Object.defineProperty(navigator, 'platform', {
                    get: () => 'Win32',
                });

                // Simuler hardwareConcurrency réaliste
                Object.defineProperty(navigator, 'hardwareConcurrency', {
                    get: () => 4,
                });

                // Simuler deviceMemory
                Object.defineProperty(navigator, 'deviceMemory', {
                    get: () => 8,
                });

                // Simuler une connexion non-automatisée
                Object.defineProperty(navigator, 'connection', {
                    get: () => {
                        return {
                            effectiveType: '4g',
                            rtt: Math.floor(Math.random() * 50) + 30,
                            downlink: Math.floor(Math.random() * 5) + 8,
                            saveData: false
                        };
                    },
                });

                // Simuler des fonctionnalités de batterie
                if (navigator.getBattery) {
                    navigator.getBattery = function() {
                        return Promise.resolve({
                            charging: Math.random() > 0.5,
                            chargingTime: Math.random() > 0.5 ? 0 : Math.floor(Math.random() * 7200),
                            dischargingTime: Math.random() > 0.5 ? Infinity : Math.floor(Math.random() * 28800),
                            level: Math.random() * 0.99 + 0.01,
                            addEventListener: function() {},
                            removeEventListener: function() {}
                        });
                    };
                }

                // Simuler des fonctionnalités de permissions
                if (navigator.permissions) {
                    const originalQuery = navigator.permissions.query;
                    navigator.permissions.query = function(parameters) {
                        const states = ['granted', 'denied', 'prompt'];
                        const randomState = states[Math.floor(Math.random() * states.length)];
                        return Promise.resolve({state: randomState, onchange: null});
                    };
                }

                // Simuler des propriétés d'écran réalistes
                Object.defineProperty(screen, 'availTop', {
                    get: () => 0,
                });
                Object.defineProperty(screen, 'availLeft', {
                    get: () => 0,
                });

                // Masquer les traces de headless
                Object.defineProperty(navigator, 'maxTouchPoints', {
                    get: () => 0,
                });

                // Simuler des événements de souris et clavier naturels
                const originalAddEventListener = EventTarget.prototype.addEventListener;
                EventTarget.prototype.addEventListener = function(type, listener, options) {
                    // Ajouter un léger délai aléatoire pour simuler le comportement humain
                    if (type === 'click' || type === 'mousedown' || type === 'mouseup') {
                        const originalListener = listener;
                        listener = function(event) {
                            setTimeout(() => originalListener.call(this, event), Math.random() * 10);
                        };
                    }
                    return originalAddEventListener.call(this, type, listener, options);
                };

                // Simuler des propriétés CSS media queries
                const originalMatchMedia = window.matchMedia;
                window.matchMedia = function(query) {
                    const result = originalMatchMedia.call(this, query);
                    if (query.includes('prefers-reduced-motion')) {
                        result.matches = false;
                    }
                    return result;
                };
            """)

            # Configuration des timeouts avec une légère variation
            timeout_base = self.config["browser"]["navigation_timeout"] * 1000
            timeout_variation = random.randint(-1000, 1000)  # Variation de ±1 seconde
            context.set_default_timeout(timeout_base + timeout_variation)
            context.set_default_navigation_timeout(timeout_base + timeout_variation)

            return browser, context

    async def _random_delay(self, min_seconds: float = 1.0, max_seconds: float = 3.0):
        """Ajoute un délai aléatoire pour simuler un comportement humain"""
        delay = random.uniform(min_seconds, max_seconds)
        self.logger.debug(f"Délai aléatoire: {delay:.2f}s")
        await asyncio.sleep(delay)

    async def _human_like_delay(self, action_type: str = "default"):
        """Délais spécifiques selon le type d'action pour simuler un comportement humain"""
        delays = {
            "page_load": (3.0, 8.0),      # Attendre le chargement de page
            "click": (0.5, 2.0),          # Avant/après un clic
            "type": (0.1, 0.3),           # Entre les caractères
            "navigation": (2.0, 5.0),     # Navigation entre pages
            "captcha": (5.0, 15.0),       # Résolution de captcha
            "login": (1.0, 3.0),          # Actions de connexion
            "scroll": (0.5, 1.5),         # Défilement
            "default": (1.0, 3.0)         # Délai par défaut
        }

        min_delay, max_delay = delays.get(action_type, delays["default"])
        delay = random.uniform(min_delay, max_delay)
        self.logger.debug(f"Délai humain ({action_type}): {delay:.2f}s")
        await asyncio.sleep(delay)

    async def _simulate_human_mouse_movement(self, page, element):
        """Simule un mouvement de souris humain vers un élément"""
        try:
            # Obtenir la position de l'élément
            box = await element.bounding_box()
            if not box:
                return

            # Calculer le centre de l'élément avec un léger décalage aléatoire
            center_x = box['x'] + box['width'] / 2 + random.uniform(-10, 10)
            center_y = box['y'] + box['height'] / 2 + random.uniform(-10, 10)

            # Simuler un mouvement de souris en plusieurs étapes
            current_pos = await page.evaluate("() => ({ x: window.mouseX || 0, y: window.mouseY || 0 })")
            start_x = current_pos.get('x', 0)
            start_y = current_pos.get('y', 0)

            # Créer un chemin de mouvement en courbe
            steps = random.randint(3, 7)
            for i in range(steps):
                progress = (i + 1) / steps
                # Ajouter une courbe naturelle
                curve_offset_x = random.uniform(-20, 20) * (1 - progress)
                curve_offset_y = random.uniform(-20, 20) * (1 - progress)

                intermediate_x = start_x + (center_x - start_x) * progress + curve_offset_x
                intermediate_y = start_y + (center_y - start_y) * progress + curve_offset_y

                await page.mouse.move(intermediate_x, intermediate_y)
                await asyncio.sleep(random.uniform(0.01, 0.05))

            # Enregistrer la position finale
            await page.evaluate(f"() => {{ window.mouseX = {center_x}; window.mouseY = {center_y}; }}")

        except Exception as e:
            self.logger.debug(f"Erreur lors de la simulation de mouvement de souris: {e}")

    async def _human_like_click(self, page, element, delay_before: bool = True, delay_after: bool = True):
        """Effectue un clic avec un comportement humain"""
        try:
            if delay_before:
                await self._human_like_delay("click")

            # Simuler le mouvement de souris vers l'élément
            await self._simulate_human_mouse_movement(page, element)

            # Petit délai avant le clic
            await asyncio.sleep(random.uniform(0.1, 0.3))

            # Clic avec un léger délai de maintien
            await element.click(delay=random.randint(50, 150))

            if delay_after:
                await self._human_like_delay("click")

        except Exception as e:
            self.logger.debug(f"Erreur lors du clic humain: {e}")
            # Fallback vers un clic normal
            await element.click()

    async def _human_like_type(self, page, element, text: str, delay_between_chars: bool = True):
        """Tape du texte avec un comportement humain"""
        try:
            await element.click()  # Focus sur l'élément
            await self._human_like_delay("click")

            if delay_between_chars:
                # Taper caractère par caractère avec des délais variables
                for char in text:
                    await element.type(char, delay=random.randint(50, 200))
                    # Délai aléatoire entre les caractères
                    if random.random() < 0.1:  # 10% de chance d'une pause plus longue
                        await asyncio.sleep(random.uniform(0.2, 0.5))
            else:
                await element.type(text, delay=random.randint(50, 100))

            await self._human_like_delay("type")

        except Exception as e:
            self.logger.debug(f"Erreur lors de la saisie humaine: {e}")
            # Fallback vers une saisie normale
            await element.fill(text)

    async def _handle_proxy_error(self, error: Exception):
        """Gère les erreurs liées au proxy et effectue une rotation si nécessaire"""
        if self.proxy_rotator and self.proxy_rotator.current_proxy:
            self.logger.warning(f"Erreur détectée avec le proxy actuel: {error}")
            self.proxy_rotator.report_proxy_failure(self.proxy_rotator.current_proxy)

            # Forcer une rotation si le proxy a trop d'échecs
            if self.proxy_rotator._proxy_has_too_many_failures(self.proxy_rotator.current_proxy):
                self.logger.info("Rotation forcée du proxy en raison de trop d'échecs")
                await self.proxy_rotator.force_rotation()

    def _get_memory_usage(self) -> float:
        """Récupère l'utilisation actuelle de la mémoire en MB"""
        # Utiliser une valeur simulée pour éviter les problèmes de dépendances
        # Dans un environnement de production, vous pourriez installer psutil
        return 100.0  # Valeur simulée en MB

    async def simulate_human_behavior(self, page: Page) -> None:
        """Simule un comportement humain sur la page pour éviter la détection de bot"""
        try:
            self.logger.info("Simulation d'un comportement humain sur la page...")

            # 1. Faire défiler la page de manière aléatoire
            page_height = await page.evaluate("() => document.body.scrollHeight")
            viewport_height = await page.evaluate("() => window.innerHeight")

            # Nombre de défilements aléatoire
            num_scrolls = random.randint(2, 5)

            for _ in range(num_scrolls):
                # Position de défilement aléatoire
                scroll_y = random.randint(100, int(page_height * 0.8))

                # Défilement avec une vitesse variable (plus lent = plus humain)
                await page.evaluate(f"""() => {{
                    const duration = {random.randint(500, 1500)};
                    const start = window.scrollY;
                    const change = {scroll_y} - start;
                    let startTime = null;

                    function easeInOutQuad(t) {{
                        return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;
                    }}

                    function animateScroll(timestamp) {{
                        if (!startTime) startTime = timestamp;
                        const elapsed = timestamp - startTime;
                        const progress = Math.min(elapsed / duration, 1);
                        const eased = easeInOutQuad(progress);
                        window.scrollTo(0, start + change * eased);

                        if (elapsed < duration) {{
                            window.requestAnimationFrame(animateScroll);
                        }}
                    }}

                    window.requestAnimationFrame(animateScroll);
                }}""")

                # Pause aléatoire entre les défilements
                await page.wait_for_timeout(random.randint(1000, 3000))

            # 2. Simuler des mouvements de souris aléatoires
            for _ in range(random.randint(3, 7)):
                # Coordonnées aléatoires dans la fenêtre visible
                x = random.randint(10, await page.evaluate("() => window.innerWidth") - 10)
                y = random.randint(10, viewport_height - 10)

                # Mouvement de souris avec une vitesse variable
                await page.mouse.move(x, y, steps=random.randint(5, 15))

                # Pause aléatoire entre les mouvements
                await page.wait_for_timeout(random.randint(500, 2000))

            # 3. Simuler un survol sur des éléments interactifs
            interactive_selectors = [
                "a", "button", "input", "select", "textarea",
                "[role='button']", "[role='link']", "[role='tab']",
                ".btn", ".button", ".nav-item", ".menu-item"
            ]

            for selector in interactive_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    if elements and len(elements) > 0:
                        # Choisir un élément aléatoire
                        random_index = random.randint(0, min(len(elements) - 1, 5))  # Limiter à 5 pour éviter trop d'éléments
                        element = elements[random_index]

                        # Obtenir la position de l'élément
                        bounding_box = await element.bounding_box()
                        if bounding_box:
                            # Calculer le centre de l'élément
                            center_x = bounding_box["x"] + bounding_box["width"] / 2
                            center_y = bounding_box["y"] + bounding_box["height"] / 2

                            # Vérifier si l'élément est visible dans la fenêtre
                            if (center_y > 0 and center_y < viewport_height):
                                # Déplacer la souris vers l'élément
                                await page.mouse.move(center_x, center_y, steps=random.randint(5, 10))

                                # Pause aléatoire sur l'élément
                                await page.wait_for_timeout(random.randint(500, 1500))
                                break
                except Exception as e:
                    self.logger.debug(f"Erreur lors de l'interaction avec {selector}: {e}")

            # 4. Pause finale aléatoire pour simuler la lecture
            await page.wait_for_timeout(random.randint(2000, 5000))

            self.logger.info("Comportement humain simulé avec succès")
        except Exception as e:
            self.logger.warning(f"Erreur lors de la simulation du comportement humain: {e}")
            # Ne pas échouer si cette fonction échoue

async def main():
    """Fonction principale"""
    import argparse

    parser = argparse.ArgumentParser(description="Renouvellement automatique des annonces SeLogerPro")
    parser.add_argument("--config", "-c", default="config.json", help="Chemin vers le fichier de configuration")
    parser.add_argument("--simulation", "-s", action="store_true", help="Exécuter en mode simulation (sans connexion réelle)")
    parser.add_argument("--verbose", "-v", action="store_true", help="Afficher les messages de débogage détaillés")
    parser.add_argument("--visible", "-d", action="store_true", help="Afficher le navigateur (mode non-headless)")
    parser.add_argument("--url", "-u", help="URL de départ personnalisée (par défaut: https://myselogerpro.com/login)")
    parser.add_argument("--username", help="Nom d'utilisateur (remplace celui du fichier config)")
    parser.add_argument("--password", help="Mot de passe (remplace celui du fichier config)")
    parser.add_argument("--continue-after-captcha-failure", "-f", action="store_true", help="Continuer l'exécution même après un échec de captcha")
    args = parser.parse_args()

    # Configuration du niveau de journalisation
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        print("Mode verbeux activé - affichage des messages de débogage")

    # Vérification de l'existence du fichier de configuration
    if not os.path.exists(args.config):
        print(f"Erreur: Le fichier de configuration '{args.config}' n'existe pas.")
        print("Création d'un fichier de configuration par défaut...")

        # Création d'un fichier de configuration par défaut
        with open(args.config, 'w') as f:
            json.dump({
                "auth": {
                    "username": "VOTRE_IDENTIFIANT",
                    "password": "VOTRE_MOT_DE_PASSE",
                    "2fa_enabled": False,
                    "encryption": {
                        "algorithm": "AES-256-CBC",
                        "key_derivation_iterations": 100000
                    }
                },
                "renewal": {
                    "strategy": "create_new_from_template",
                    "schedule": {
                        "frequency": "daily",
                        "execution_window": {
                            "start": "12:45",
                            "end": "13:15"
                        },
                        "random_delay": {
                            "min_seconds": 60,
                            "max_seconds": 300
                        }
                    },
                    "listing_age_threshold_days": 4,
                    "max_properties_per_run": 0,
                    "template_management": {
                        "keep_template_data": [
                            "title", "description", "photos", "price_history", "characteristics"
                        ],
                        "archive_old_listings": True,
                        "archive_retention_days": 7
                    }
                },
                "notifications": {
                    "webhook": {
                        "url": "https://votre-webapp.com/api/renewal-events",
                        "headers": {
                            "Content-Type": "application/json",
                            "X-API-Key": "VOTRE_API_KEY"
                        },
                        "on_error_only": True,
                        "include_logs_in_webhook": True
                    }
                },
                "error_handling": {
                    "retry_policy": {
                        "max_retries": 3,
                        "backoff_factor": 2.5,
                        "retryable_status_codes": [500, 502, 503, 504]
                    },
                    "error_logs": {
                        "detailed_tracing": True,
                        "screenshot_on_error": True,
                        "retention_days": 14
                    }
                },
                "logging": {
                    "level": "INFO",
                    "cloud": {
                        "provider": "aws_s3",
                        "bucket": "votre-bucket-logs",
                        "path": "selogerpro/renewal-logs/",
                        "log_rotation_days": 1
                    },
                    "performance_metrics": {
                        "track": ["execution_time", "memory_usage", "network_latency"]
                    }
                }
            }, f, indent=4)

        print(f"Fichier de configuration créé: {args.config}")
        print("Veuillez éditer ce fichier avec vos informations avant de relancer le script.")
        return

    # Création et exécution du renouveleur
    print(f"Démarrage du renouvellement des annonces SeLogerPro...")

    # Affichage des options activées
    if args.simulation:
        print("MODE SIMULATION ACTIVÉ - Aucune connexion réelle ne sera effectuée")
    if args.visible:
        print("MODE VISIBLE ACTIVÉ - Le navigateur sera affiché pendant l'exécution")
    if args.url:
        print(f"URL PERSONNALISÉE: {args.url}")

    # Création du renouveleur avec les options
    renewer = SeLogerRenewer(
        args.config,
        simulation_mode=args.simulation,
        continue_after_captcha_failure=args.continue_after_captcha_failure
    )

    # Configuration des options supplémentaires
    if args.visible:
        renewer.headless = False

    if args.url:
        # Stocker l'URL personnalisée pour l'utiliser dans execute_workflow
        renewer.custom_url = args.url

    # Remplacer les identifiants si fournis en ligne de commande
    if args.username:
        print(f"Utilisation du nom d'utilisateur fourni en ligne de commande")
        renewer.config["auth"]["username"] = args.username

    if args.password:
        print(f"Utilisation du mot de passe fourni en ligne de commande")
        renewer.config["auth"]["password"] = args.password

    # Exécution du workflow
    results = await renewer.execute_workflow()

    # Affichage des résultats
    print("\nRésumé de l'exécution:")
    print(f"- Annonces renouvelées: {results['total_renewed']}")
    print(f"- Échecs: {results['total_failed']}")
    print(f"- Erreurs: {len(results['errors'])}")

    if results['errors']:
        print("\nErreurs rencontrées:")
        for i, error in enumerate(results['errors'][:5], 1):  # Afficher max 5 erreurs
            print(f"  {i}. {error}")

        if len(results['errors']) > 5:
            print(f"  ... et {len(results['errors']) - 5} autres erreurs (voir les logs pour plus de détails)")

    # Affichage des métriques de performance si disponibles
    if 'performance_metrics' in results and results['performance_metrics']:
        print("\nMétriques de performance:")
        metrics = results['performance_metrics']
        if 'execution_time' in metrics:
            print(f"- Temps d'exécution total: {metrics['execution_time']:.2f} secondes")
        if 'avg_renewal_time' in metrics and metrics['avg_renewal_time']:
            print(f"- Temps moyen par annonce: {metrics['avg_renewal_time']:.2f} secondes")

    print("\nRenouvellement terminé.")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nOpération annulée par l'utilisateur.")
    except Exception as e:
        print(f"\nErreur critique: {e}")
        print("Veuillez vérifier les logs pour plus de détails.")
