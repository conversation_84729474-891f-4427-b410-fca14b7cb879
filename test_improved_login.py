#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test des améliorations de connexion avec variabilité humaine
"""

import asyncio
import json
import random
import logging
from playwright.async_api import async_playwright

async def test_variable_login_strategies():
    """Teste les différentes stratégies de connexion variables"""
    
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # Charger la configuration
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    username = config['auth']['username']
    password = config['auth']['password']
    
    async with async_playwright() as p:
        # Lancer le navigateur avec proxy
        proxy_config = {
            "server": "http://geo.iproyal.com:12321",
            "username": "JNe3xA4xAmsLLA9G",
            "password": "7aa7BQB4XDcn6SKn_country-fr"
        }
        
        browser = await p.chromium.launch(
            headless=False,
            proxy=proxy_config,
            args=[
                '--no-first-run',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        )
        
        context = await browser.new_context(
            viewport={'width': 1366, 'height': 768},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        
        page = await context.new_page()
        
        try:
            # Pause initiale variable
            initial_delay = random.randint(2, 8)
            logger.info(f"Pause initiale de {initial_delay} secondes...")
            await asyncio.sleep(initial_delay)
            
            # Navigation avec comportement humain
            logger.info("Navigation vers SeLogerPro...")
            await page.goto("https://myselogerpro.com", timeout=30000)
            await page.wait_for_load_state("domcontentloaded")
            
            # Simulation de lecture de la page
            logger.info("Simulation de lecture de la page d'accueil...")
            await page.evaluate("""
                () => {
                    // Simuler des mouvements de souris
                    for (let i = 0; i < 3; i++) {
                        setTimeout(() => {
                            const x = Math.random() * window.innerWidth;
                            const y = Math.random() * window.innerHeight;
                            const event = new MouseEvent('mousemove', { clientX: x, clientY: y });
                            document.dispatchEvent(event);
                        }, i * 1000);
                    }
                }
            """)
            
            await asyncio.sleep(random.uniform(3, 7))
            
            # Aller vers la page de connexion
            logger.info("Navigation vers la page de connexion...")
            await page.goto("https://myselogerpro.com/login", timeout=30000)
            await page.wait_for_load_state("networkidle")
            
            # Accepter les cookies si nécessaire
            try:
                cookie_button = await page.query_selector("button:has-text('Accepter')")
                if cookie_button:
                    logger.info("Acceptation des cookies...")
                    await cookie_button.click()
                    await asyncio.sleep(random.uniform(1, 3))
            except:
                pass
            
            # Trouver les champs de connexion
            email_field = await page.query_selector("input:first-of-type")
            password_field = await page.query_selector("input[type='password']")
            login_button = await page.query_selector("button:has-text('Me connecter')")
            
            if not email_field or not password_field or not login_button:
                logger.error("Impossible de trouver les champs de connexion")
                return False
            
            # Choisir une stratégie de remplissage aléatoire
            strategies = ['fast_fill', 'normal_type', 'mixed', 'human_like']
            strategy = random.choice(strategies)
            logger.info(f"Stratégie sélectionnée: {strategy}")
            
            if strategy == 'fast_fill':
                # Simulation copier-coller
                logger.info("Remplissage rapide (copier-coller)")
                await email_field.fill(username)
                await asyncio.sleep(random.uniform(0.3, 0.8))
                
                # Navigation par Tab ou clic
                if random.random() > 0.5:
                    await page.keyboard.press('Tab')
                    logger.info("Navigation par Tab")
                else:
                    await password_field.click()
                    logger.info("Navigation par clic")
                
                await asyncio.sleep(random.uniform(0.2, 0.6))
                await password_field.fill(password)
                
            elif strategy == 'normal_type':
                # Frappe normale
                logger.info("Frappe normale")
                await email_field.click()
                await email_field.click(click_count=3)  # Sélectionner tout
                await email_field.press("Backspace")  # Effacer
                await email_field.type(username, delay=random.randint(80, 200))

                await asyncio.sleep(random.uniform(0.5, 1.2))

                if random.random() > 0.3:
                    await page.keyboard.press('Tab')
                else:
                    await password_field.click()

                await password_field.click(click_count=3)  # Sélectionner tout
                await password_field.press("Backspace")  # Effacer
                await password_field.type(password, delay=random.randint(100, 250))
                
            elif strategy == 'mixed':
                # Email rapide, mot de passe lent
                logger.info("Stratégie mixte")
                await email_field.fill(username)
                await asyncio.sleep(random.uniform(0.5, 1.5))
                
                await page.keyboard.press('Tab')
                await asyncio.sleep(random.uniform(0.3, 0.8))
                
                await password_field.click(click_count=3)  # Sélectionner tout
                await password_field.press("Backspace")  # Effacer
                await password_field.type(password, delay=random.randint(150, 350))
                
            else:  # human_like
                # Frappe avec erreurs occasionnelles
                logger.info("Frappe humaine avec erreurs")
                await email_field.click()
                await email_field.click(click_count=3)  # Sélectionner tout
                await email_field.press("Backspace")  # Effacer
                
                for i, char in enumerate(username):
                    # Erreur occasionnelle
                    if random.random() < 0.03 and i > 0:
                        wrong_char = chr(ord(char) + random.randint(1, 3))
                        await email_field.type(wrong_char, delay=random.randint(100, 200))
                        await asyncio.sleep(random.uniform(0.2, 0.5))
                        await email_field.press("Backspace")
                    
                    await email_field.type(char, delay=random.randint(80, 180))
                
                await asyncio.sleep(random.uniform(0.8, 1.8))
                
                if random.random() > 0.4:
                    await page.keyboard.press('Tab')
                else:
                    await password_field.click()
                
                await asyncio.sleep(random.uniform(0.4, 1.0))
                await password_field.click(click_count=3)  # Sélectionner tout
                await password_field.press("Backspace")  # Effacer
                
                for char in password:
                    await password_field.type(char, delay=random.randint(120, 250))
            
            # Pause avant de cliquer sur le bouton
            await asyncio.sleep(random.uniform(1, 3))
            
            # Clic sur le bouton avec variabilité
            logger.info("Clic sur le bouton de connexion...")
            
            # Parfois cliquer directement, parfois déplacer la souris d'abord
            if random.random() > 0.3:
                # Déplacer la souris vers le bouton
                box = await login_button.bounding_box()
                if box:
                    center_x = box["x"] + box["width"] / 2
                    center_y = box["y"] + box["height"] / 2
                    
                    # Ajouter une variation
                    offset_x = random.uniform(-box["width"] * 0.2, box["width"] * 0.2)
                    offset_y = random.uniform(-box["height"] * 0.2, box["height"] * 0.2)
                    
                    await page.mouse.move(center_x + offset_x, center_y + offset_y)
                    await asyncio.sleep(random.uniform(0.2, 0.6))
                    await page.mouse.click(center_x + offset_x, center_y + offset_y)
            else:
                # Clic direct
                await login_button.click()
            
            # Attendre la réponse
            logger.info("Attente de la réponse...")
            await page.wait_for_load_state("domcontentloaded", timeout=30000)
            
            # Vérifier le résultat
            await asyncio.sleep(3)
            current_url = page.url
            
            # Prendre une capture d'écran
            await page.screenshot(path=f"screenshots/login_test_{strategy}.png")
            
            if "login" in current_url:
                # Vérifier s'il y a un captcha
                captcha_present = await page.query_selector("iframe[src*='captcha']")
                if captcha_present:
                    logger.info("✅ Succès partiel: Arrivé au captcha (pas de blocage)")
                    return True
                else:
                    logger.warning("❌ Toujours sur la page de login")
                    return False
            else:
                logger.info("✅ Succès: Connexion réussie")
                return True
                
        except Exception as e:
            logger.error(f"❌ Erreur: {e}")
            return False
        finally:
            await browser.close()

async def test_multiple_strategies():
    """Teste plusieurs stratégies pour voir laquelle fonctionne le mieux"""
    
    logger = logging.getLogger(__name__)
    logger.info("🧪 Test de multiples stratégies de connexion")
    
    results = {}
    strategies = ['fast_fill', 'normal_type', 'mixed', 'human_like']
    
    for i in range(4):  # Tester 4 fois
        strategy = strategies[i % len(strategies)]
        logger.info(f"\n🔄 Test {i+1}/4 avec stratégie: {strategy}")
        
        # Attendre entre les tests
        if i > 0:
            wait_time = random.randint(30, 60)
            logger.info(f"Attente de {wait_time} secondes entre les tests...")
            await asyncio.sleep(wait_time)
        
        success = await test_variable_login_strategies()
        results[f"test_{i+1}_{strategy}"] = success
        
        logger.info(f"Résultat: {'✅ Succès' if success else '❌ Échec'}")
    
    # Résumé
    logger.info("\n📊 Résumé des tests:")
    successes = sum(1 for success in results.values() if success)
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ Succès" if success else "❌ Échec"
        logger.info(f"  {test_name}: {status}")
    
    logger.info(f"\n🎯 Taux de réussite: {successes}/{total} ({successes/total*100:.1f}%)")
    
    if successes > total * 0.5:
        logger.info("🎉 Les améliorations semblent efficaces!")
    else:
        logger.info("⚠️  Besoin d'ajustements supplémentaires")

def main():
    """Fonction principale"""
    print("🛡️  Test des améliorations de connexion variable")
    print("=" * 50)
    
    try:
        asyncio.run(test_multiple_strategies())
    except KeyboardInterrupt:
        print("\n⏹️  Test interrompu par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur lors du test: {e}")

if __name__ == "__main__":
    main()
