{"auth": {"username": "RC-009048", "password": "Actifs-100919", "2fa_enabled": false, "encryption": {"algorithm": "AES-256-CBC", "key_derivation_iterations": 100000}, "captcha": {"service": "2captcha", "api_key": "e7aac790e1831ff1c792a610fdf41754"}}, "renewal": {"strategy": "create_new_from_template", "schedule": {"frequency": "daily", "execution_window": {"start": "12:45", "end": "13:15"}, "random_delay": {"min_seconds": 60, "max_seconds": 300}}, "listing_age_threshold_days": 4, "max_properties_per_run": 0, "template_management": {"keep_template_data": ["title", "description", "photos", "price_history", "characteristics"], "archive_old_listings": true, "archive_retention_days": 7}}, "notifications": {"webhook": {"url": "https://ton-webapp.com/api/renewal-events", "headers": {"Content-Type": "application/json", "X-API-Key": "TON_API_KEY"}, "on_error_only": true, "include_logs_in_webhook": true}}, "error_handling": {"retry_policy": {"max_retries": 3, "backoff_factor": 2.5, "retryable_status_codes": [500, 502, 503, 504]}, "error_logs": {"detailed_tracing": true, "screenshot_on_error": true, "retention_days": 14}}, "logging": {"level": "DEBUG", "cloud": {"provider": "aws_s3", "bucket": "ton-log-bucket", "path": "selogerpro/renewal-logs/", "log_rotation_days": 1}, "performance_metrics": {"track": ["execution_time", "memory_usage", "network_latency"]}}, "browser": {"type": "chromium", "launch_timeout": 30, "navigation_timeout": 30}, "proxy": {"enabled": true, "hostname": "geo.iproyal.com", "port": 12321, "username": "JNe3xA4xAmsLLA9G", "password": "7aa7BQB4XDcn6SKn", "countries": ["fr", "de", "es", "it", "nl", "be"], "rotation_interval_minutes": 10, "max_failures_per_proxy": 3, "timeout_seconds": 30, "force_new_session": true, "session_rotation_per_run": true}, "privacy": {"anonymization": {"credentials": true, "ip_address": false}, "data_retention_days": 14, "gdpr_compliance": {"auto_purge": true, "purge_schedule": "weekly"}}, "webapp_integration": {"renewal_threshold_editable": true, "state_saving": {"enabled": true, "storage": {"type": "redis", "ttl_hours": 336}, "tracked_metrics": ["renewed_listings", "failed_attempts", "average_processing_time"]}, "reporting": {"generate_reports": true, "formats": ["pdf", "csv"], "schedule": "weekly"}}}