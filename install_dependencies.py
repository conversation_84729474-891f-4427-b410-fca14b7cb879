#!/usr/bin/env python3
"""
Script d'installation des dépendances pour le projet SeLogerPro
"""

import subprocess
import sys
import os
import json

def run_command(command, description):
    """Exécute une commande et affiche le résultat"""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} - Succès")
        if result.stdout:
            print(f"   Output: {result.stdout.strip()}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} - Échec")
        print(f"   Erreur: {e.stderr.strip()}")
        return False

def check_python_version():
    """Vérifie la version de Python"""
    print("🔍 Vérification de la version Python...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - Version trop ancienne (requis: 3.8+)")
        return False

def install_requirements():
    """Installe les dépendances Python"""
    if not os.path.exists("requirements.txt"):
        print("❌ Fichier requirements.txt non trouvé")
        return False
    
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "Installation des dépendances Python"
    )

def install_playwright():
    """Installe les navigateurs Playwright"""
    return run_command(
        f"{sys.executable} -m playwright install",
        "Installation des navigateurs Playwright"
    )

def create_directories():
    """Crée les répertoires nécessaires"""
    directories = ["screenshots", "auth", "data"]
    
    for directory in directories:
        if not os.path.exists(directory):
            try:
                os.makedirs(directory)
                print(f"✅ Répertoire '{directory}' créé")
            except Exception as e:
                print(f"❌ Erreur lors de la création du répertoire '{directory}': {e}")
                return False
        else:
            print(f"ℹ️  Répertoire '{directory}' existe déjà")
    
    return True

def check_config_file():
    """Vérifie et valide le fichier de configuration"""
    print("\n🔍 Vérification du fichier de configuration...")
    
    if not os.path.exists("config.json"):
        print("⚠️  Fichier config.json non trouvé")
        print("   Veuillez créer un fichier config.json basé sur l'exemple du README")
        return False
    
    try:
        with open("config.json", "r") as f:
            config = json.load(f)
        
        # Vérifications de base
        required_sections = ["auth", "browser"]
        for section in required_sections:
            if section not in config:
                print(f"❌ Section '{section}' manquante dans config.json")
                return False
        
        # Vérifier les informations d'authentification
        auth = config["auth"]
        if not auth.get("username") or auth["username"] == "VOTRE_IDENTIFIANT":
            print("⚠️  Nom d'utilisateur non configuré dans config.json")
            print("   Veuillez mettre à jour le champ 'username' dans la section 'auth'")
        
        if not auth.get("password") or auth["password"] == "VOTRE_MOT_DE_PASSE":
            print("⚠️  Mot de passe non configuré dans config.json")
            print("   Veuillez mettre à jour le champ 'password' dans la section 'auth'")
        
        # Vérifier la configuration proxy si activée
        if config.get("proxy", {}).get("enabled", False):
            proxy = config["proxy"]
            if proxy.get("username") == "VOTRE_USERNAME_IPROYAL":
                print("⚠️  Informations proxy IProyal non configurées")
                print("   Veuillez mettre à jour les champs proxy dans config.json")
        
        print("✅ Fichier config.json trouvé et valide")
        return True
        
    except json.JSONDecodeError:
        print("❌ Erreur de format dans config.json")
        return False
    except Exception as e:
        print(f"❌ Erreur lors de la lecture de config.json: {e}")
        return False

def test_installation():
    """Teste l'installation en important les modules principaux"""
    print("\n🧪 Test de l'installation...")
    
    try:
        import playwright
        print("✅ Playwright importé avec succès")
    except ImportError:
        print("❌ Impossible d'importer Playwright")
        return False
    
    try:
        import aiohttp
        print("✅ aiohttp importé avec succès")
    except ImportError:
        print("❌ Impossible d'importer aiohttp")
        return False
    
    try:
        from seloger_renewer import ProxyRotator
        print("✅ ProxyRotator importé avec succès")
    except ImportError as e:
        print(f"❌ Impossible d'importer ProxyRotator: {e}")
        return False
    
    print("✅ Tous les modules principaux sont disponibles")
    return True

def main():
    """Fonction principale d'installation"""
    print("🚀 Installation des dépendances SeLogerPro")
    print("=" * 50)
    
    success = True
    
    # Vérification de Python
    if not check_python_version():
        success = False
    
    # Création des répertoires
    if not create_directories():
        success = False
    
    # Installation des dépendances
    if not install_requirements():
        success = False
    
    # Installation de Playwright
    if not install_playwright():
        success = False
    
    # Test de l'installation
    if not test_installation():
        success = False
    
    # Vérification de la configuration
    check_config_file()  # Non bloquant
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Installation terminée avec succès!")
        print("\nÉtapes suivantes:")
        print("1. Configurez votre fichier config.json avec vos identifiants")
        print("2. Si vous utilisez des proxies, configurez vos informations IProyal")
        print("3. Testez la configuration avec: python test_proxy.py")
        print("4. Lancez le script avec: python seloger_renewer.py --visible")
    else:
        print("❌ Installation terminée avec des erreurs")
        print("Veuillez corriger les erreurs ci-dessus avant de continuer")
        sys.exit(1)

if __name__ == "__main__":
    main()
