# Configuration Proxy IProyal pour SeLogerPro

Ce guide explique comment configurer et utiliser les proxies résidentiels IProyal avec l'automatisation SeLogerPro.

## 🔧 Configuration

### 1. Configuration dans config.json

Votre fichier `config.json` doit contenir la section proxy suivante :

```json
{
  "proxy": {
    "enabled": true,
    "hostname": "geo.iproyal.com",
    "port": 12321,
    "username": "JNe3xA4xAmsLLA9G",
    "password": "7aa7BQB4XDcn6SKn",
    "countries": ["fr", "de", "es", "it", "nl", "be"],
    "rotation_interval_minutes": 10,
    "max_failures_per_proxy": 3,
    "timeout_seconds": 30,
    "force_new_session": true,
    "session_rotation_per_run": true
  }
}
```

### 2. Paramètres expliqués

- **enabled**: Active/désactive l'utilisation des proxies
- **hostname**: Serveur proxy IProyal (geo.iproyal.com)
- **port**: Port du proxy (12321 pour les proxies résidentiels)
- **username**: Votre nom d'utilisateur IProyal
- **password**: Votre mot de passe IProyal
- **countries**: Liste des pays à utiliser pour la rotation géographique
- **rotation_interval_minutes**: Intervalle de rotation automatique (en minutes)
- **max_failures_per_proxy**: Nombre d'échecs avant rotation forcée
- **timeout_seconds**: Timeout pour les tests de connectivité
- **force_new_session**: Force une nouvelle session IP à chaque utilisation
- **session_rotation_per_run**: Nouvelle session pour chaque exécution

## 🧪 Tests

### Test de configuration de base
```bash
python test_proxy.py
```

### Test de connectivité uniquement
```bash
python test_proxy.py --connectivity
```

### Test complet (configuration + connectivité)
```bash
python test_proxy.py --full
```

### Test avec Playwright (mode réel)
```bash
# Test complet avec navigateur
python test_proxy_real.py

# Test de rotation uniquement
python test_proxy_real.py --rotation

# Test avec Playwright uniquement
python test_proxy_real.py --playwright
```

### Mode verbeux
```bash
python test_proxy.py --verbose
python test_proxy_real.py --verbose
```

## 🚀 Utilisation

### Lancement avec proxy
```bash
# Mode normal avec proxy
python seloger_renewer.py --visible

# Mode simulation avec proxy
python seloger_renewer.py --simulation --visible

# Mode verbeux pour voir les détails du proxy
python seloger_renewer.py --verbose --visible
```

## 📊 Fonctionnalités du Proxy

### 1. Rotation automatique des IP
- Nouvelle session IP à chaque exécution
- Rotation géographique entre les pays configurés
- Rotation automatique en cas d'échec

### 2. Gestion des erreurs
- Détection automatique des proxies défaillants
- Rotation forcée après un nombre d'échecs configuré
- Fallback vers d'autres pays en cas de problème

### 3. Optimisations
- Test de connectivité avant utilisation
- Timeout configurables
- Gestion des sessions pour éviter la détection

## 🔍 Vérification du fonctionnement

### Dans les logs, vous devriez voir :
```
INFO - ProxyRotator initialisé avec 6 pays disponibles
INFO - Force nouvelle session: True
INFO - Rotation de session par exécution: True
INFO - Initialisation du proxy IProyal...
INFO - Nouvelle session générée: a1b2c3d4
INFO - Test proxy réussi, IP: 185.xxx.xxx.xxx, Pays: fr
INFO - Proxy actif: fr (Session: a1b2c3d4)
INFO - Lancement du navigateur en mode visible (non-headless)
INFO - Utilisation du proxy: fr via http://geo.iproyal.com:12321
```

### Format de l'URL proxy générée :
```
http://JNe3xA4xAmsLLA9G:<EMAIL>:12321
```

### Exemple de rotation d'IP :
```
Test proxy réussi, IP: ***********, Pays: fr
Test proxy réussi, IP: *************, Pays: de
Test proxy réussi, IP: **************, Pays: es
```

## 🛠️ Dépannage

### Problème : "Proxy non activé"
- Vérifiez que `"enabled": true` dans config.json

### Problème : "Test de connectivité échoué"
- Vérifiez vos identifiants IProyal
- Testez avec curl : `curl -x http://USERNAME:<EMAIL>:12321 https://ipv4.icanhazip.com`
- Vérifiez que votre compte IProyal est actif

### Problème : "Aucun proxy disponible"
- Vérifiez la liste des pays dans `countries`
- Testez avec un seul pays : `["fr"]`
- Contactez le support IProyal

### Problème : "Rotation trop fréquente"
- Augmentez `rotation_interval_minutes`
- Augmentez `max_failures_per_proxy`

## 📝 Commandes utiles

### Test manuel avec curl
```bash
# Test de base
curl -x http://JNe3xA4xAmsLLA9G_country-fr:<EMAIL>:12321 https://ipv4.icanhazip.com

# Test avec session
curl -x http://JNe3xA4xAmsLLA9G_country-fr_session-test123:<EMAIL>:12321 https://ipv4.icanhazip.com
```

### Vérification de l'IP actuelle
```bash
curl https://ipv4.icanhazip.com
```

## 🔐 Sécurité

- Les identifiants proxy sont stockés dans config.json
- Assurez-vous que config.json n'est pas partagé publiquement
- Utilisez des variables d'environnement pour les identifiants sensibles si nécessaire

## 📞 Support

En cas de problème :
1. Lancez `python test_proxy.py --full --verbose`
2. Vérifiez les logs détaillés
3. Testez manuellement avec curl
4. Contactez le support IProyal avec les détails de l'erreur

## 🎯 Avantages de cette configuration

- ✅ Nouvelle IP à chaque exécution
- ✅ Rotation géographique automatique
- ✅ Gestion intelligente des erreurs
- ✅ Optimisé pour éviter la détection
- ✅ Tests automatiques de connectivité
- ✅ Logs détaillés pour le débogage
