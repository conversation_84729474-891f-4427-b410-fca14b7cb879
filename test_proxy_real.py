#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Script de test pour vérifier que le proxy fonctionne avec Playwright en mode réel
"""

import asyncio
import json
import logging
from playwright.async_api import async_playwright
from seloger_renewer import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

async def test_proxy_with_playwright():
    """Teste le proxy avec Playwright en mode réel"""
    
    # Configuration du logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    try:
        # Charger la configuration
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        if not config.get("proxy", {}).get("enabled", False):
            logger.error("Proxy non activé dans la configuration")
            return False
        
        logger.info("=== Test du proxy avec Playwright ===")
        
        # Créer une instance du renouveleur
        renewer = SeLogerRenewer('config.json', simulation_mode=False)
        
        # Tester le proxy
        if renewer.proxy_rotator:
            logger.info("Initialisation du proxy...")
            
            # Forcer une nouvelle session
            await renewer.proxy_rotator.force_rotation()
            current_proxy = await renewer.proxy_rotator.get_current_proxy()
            
            if current_proxy:
                logger.info(f"Proxy actuel: {current_proxy['country']} (Session: {current_proxy.get('session_id', 'N/A')})")
                
                # Tester avec Playwright
                async with async_playwright() as p:
                    logger.info("Lancement du navigateur avec proxy...")
                    
                    # Lancer le navigateur avec proxy
                    browser, context = await renewer._launch_browser(headless=False)
                    
                    try:
                        page = await context.new_page()
                        
                        # Test 1: Vérifier l'IP
                        logger.info("Test 1: Vérification de l'IP...")
                        await page.goto("https://ipv4.icanhazip.com", timeout=30000)
                        await page.wait_for_load_state("networkidle", timeout=10000)
                        
                        ip_text = await page.text_content("body")
                        if ip_text:
                            ip = ip_text.strip()
                            logger.info(f"✅ IP détectée via proxy: {ip}")
                        else:
                            logger.warning("❌ Impossible de récupérer l'IP")
                        
                        # Test 2: Vérifier avec httpbin
                        logger.info("Test 2: Vérification avec httpbin...")
                        await page.goto("http://httpbin.org/ip", timeout=30000)
                        await page.wait_for_load_state("networkidle", timeout=10000)
                        
                        # Attendre que le JSON soit chargé
                        await page.wait_for_timeout(2000)
                        
                        json_text = await page.text_content("body")
                        if json_text and "origin" in json_text:
                            logger.info(f"✅ Réponse httpbin: {json_text.strip()}")
                        else:
                            logger.warning("❌ Impossible de récupérer la réponse httpbin")
                        
                        # Test 3: Tester la navigation vers SeLogerPro
                        logger.info("Test 3: Navigation vers SeLogerPro...")
                        try:
                            await page.goto("https://myselogerpro.com", timeout=30000)
                            await page.wait_for_load_state("domcontentloaded", timeout=15000)
                            
                            title = await page.title()
                            logger.info(f"✅ Page SeLogerPro chargée: {title}")
                            
                            # Vérifier si nous sommes bloqués
                            is_blocked = await renewer.check_for_blocking(page)
                            if is_blocked:
                                logger.warning("❌ Blocage détecté sur SeLogerPro")
                            else:
                                logger.info("✅ Aucun blocage détecté")
                                
                        except Exception as nav_error:
                            logger.warning(f"❌ Erreur de navigation vers SeLogerPro: {nav_error}")
                        
                        # Attendre un peu pour voir le résultat
                        logger.info("Attente de 5 secondes pour observation...")
                        await page.wait_for_timeout(5000)
                        
                    finally:
                        await browser.close()
                        
                logger.info("✅ Test avec Playwright terminé")
                return True
            else:
                logger.error("❌ Aucun proxy disponible")
                return False
        else:
            logger.error("❌ Gestionnaire de proxy non initialisé")
            return False
            
    except Exception as e:
        logger.error(f"❌ Erreur lors du test: {e}")
        return False

async def test_proxy_rotation():
    """Teste la rotation des proxies"""
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    try:
        logger.info("=== Test de rotation des proxies ===")
        
        # Créer une instance du renouveleur
        renewer = SeLogerRenewer('config.json', simulation_mode=False)
        
        if renewer.proxy_rotator:
            # Tester 3 rotations
            for i in range(3):
                logger.info(f"\nRotation {i+1}/3:")
                
                await renewer.proxy_rotator.force_rotation()
                current_proxy = await renewer.proxy_rotator.get_current_proxy()
                
                if current_proxy:
                    logger.info(f"  Pays: {current_proxy['country']}")
                    logger.info(f"  Session: {current_proxy.get('session_id', 'N/A')}")
                    
                    # Test de connectivité
                    if await renewer.proxy_rotator.test_current_proxy_connectivity():
                        logger.info("  ✅ Connectivité OK")
                    else:
                        logger.warning("  ❌ Connectivité échouée")
                else:
                    logger.warning("  ❌ Aucun proxy disponible")
                
                # Attendre un peu entre les rotations
                await asyncio.sleep(2)
            
            logger.info("✅ Test de rotation terminé")
            return True
        else:
            logger.error("❌ Gestionnaire de proxy non initialisé")
            return False
            
    except Exception as e:
        logger.error(f"❌ Erreur lors du test de rotation: {e}")
        return False

def main():
    """Fonction principale"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test du proxy IProyal avec Playwright")
    parser.add_argument("--rotation", "-r", action="store_true", 
                       help="Test de rotation uniquement")
    parser.add_argument("--playwright", "-p", action="store_true", 
                       help="Test avec Playwright uniquement")
    
    args = parser.parse_args()
    
    print("🧪 Test du proxy IProyal avec Playwright")
    print("=" * 50)
    
    success = True
    
    if args.rotation:
        # Test de rotation uniquement
        print("Mode: Test de rotation uniquement")
        success = asyncio.run(test_proxy_rotation())
    elif args.playwright:
        # Test avec Playwright uniquement
        print("Mode: Test avec Playwright uniquement")
        success = asyncio.run(test_proxy_with_playwright())
    else:
        # Test complet
        print("Mode: Test complet (rotation + Playwright)")
        print("\n1️⃣ Test de rotation...")
        rotation_success = asyncio.run(test_proxy_rotation())
        
        print("\n2️⃣ Test avec Playwright...")
        playwright_success = asyncio.run(test_proxy_with_playwright())
        
        success = rotation_success and playwright_success
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Tous les tests ont réussi!")
        print("\n🎉 Votre configuration proxy IProyal est prête!")
        print("   - Les proxies fonctionnent correctement")
        print("   - La rotation fonctionne")
        print("   - Playwright utilise bien les proxies")
        print("   - Vous pouvez lancer l'automatisation SeLogerPro")
    else:
        print("❌ Certains tests ont échoué!")
        print("\n🔧 Vérifiez:")
        print("   - Votre connexion internet")
        print("   - Vos identifiants IProyal")
        print("   - Que votre compte IProyal est actif")

if __name__ == "__main__":
    main()
