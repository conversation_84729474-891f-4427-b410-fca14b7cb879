#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Version optimisée de SeLogerPro avec les améliorations anti-détection validées
"""

import asyncio
import json
import random
import logging
import time
import os
from playwright.async_api import async_playwright

class SeLogerOptimized:
    def __init__(self, config_path='config.json'):
        self.config_path = config_path
        self.load_config()
        self.setup_logging()
        
    def load_config(self):
        """Charge la configuration"""
        with open(self.config_path, 'r', encoding='utf-8') as f:
            self.config = json.load(f)
    
    def setup_logging(self):
        """Configure le logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    async def run_optimized_automation(self):
        """Lance l'automatisation optimisée"""
        
        self.logger.info("🚀 Démarrage de l'automatisation SeLogerPro optimisée")
        
        # Pause initiale variable (stratégie validée)
        initial_delay = random.randint(3, 12)
        self.logger.info(f"⏱️  Pause initiale de {initial_delay} secondes...")
        await asyncio.sleep(initial_delay)
        
        async with async_playwright() as p:
            # Configuration proxy (validée)
            proxy_config = {
                "server": "http://geo.iproyal.com:12321",
                "username": "JNe3xA4xAmsLLA9G",
                "password": f"7aa7BQB4XDcn6SKn_country-{random.choice(['fr', 'de', 'es', 'it', 'nl', 'be'])}"
            }
            
            self.logger.info(f"🌐 Utilisation du proxy: {proxy_config['password'].split('-')[1]}")
            
            # Lancement navigateur avec configuration anti-détection validée
            browser = await p.chromium.launch(
                headless=False,  # Mode visible pour surveillance
                proxy=proxy_config,
                args=[
                    '--no-first-run',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-dev-shm-usage',
                    '--no-sandbox'
                ]
            )
            
            # Résolution d'écran variable
            viewports = [
                {'width': 1366, 'height': 768},
                {'width': 1920, 'height': 1080},
                {'width': 1536, 'height': 864},
                {'width': 1440, 'height': 900}
            ]
            viewport = random.choice(viewports)
            
            context = await browser.new_context(
                viewport=viewport,
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            )
            
            page = await context.new_page()
            
            try:
                # Navigation humaine validée
                success = await self.human_like_navigation(page)
                
                if success:
                    self.logger.info("✅ Navigation réussie - arrivée au captcha")
                    
                    # Attendre résolution manuelle du captcha
                    self.logger.info("🔐 Veuillez résoudre le captcha manuellement...")
                    self.logger.info("⏳ Attente de 60 secondes pour résolution...")
                    
                    # Vérifier périodiquement si le captcha est résolu
                    for i in range(12):  # 12 x 5 secondes = 60 secondes
                        await asyncio.sleep(5)
                        
                        # Vérifier si nous sommes passés au-delà du captcha
                        current_url = page.url
                        if "login" not in current_url or "portefeuille" in current_url:
                            self.logger.info("✅ Captcha résolu - connexion réussie!")
                            
                            # Continuer avec le renouvellement
                            await self.perform_renewals(page)
                            break
                        
                        self.logger.info(f"⏳ Attente captcha... ({(i+1)*5}/60 secondes)")
                    
                    else:
                        self.logger.warning("⏰ Timeout - captcha non résolu dans les temps")
                
                else:
                    self.logger.error("❌ Échec de la navigation")
                    
            except Exception as e:
                self.logger.error(f"❌ Erreur: {e}")
                
            finally:
                await browser.close()
    
    async def human_like_navigation(self, page):
        """Navigation humaine validée (basée sur les tests réussis)"""
        
        try:
            # Étape 1: Page d'accueil (comportement humain)
            self.logger.info("🏠 Navigation vers la page d'accueil...")
            await page.goto("https://myselogerpro.com", timeout=30000)
            await page.wait_for_load_state("domcontentloaded")
            
            # Simulation de lecture (validée)
            self.logger.info("👀 Simulation de lecture de la page...")
            await page.evaluate("""
                () => {
                    // Mouvements de souris naturels
                    for (let i = 0; i < 4; i++) {
                        setTimeout(() => {
                            const x = Math.random() * window.innerWidth;
                            const y = Math.random() * window.innerHeight;
                            const event = new MouseEvent('mousemove', { clientX: x, clientY: y });
                            document.dispatchEvent(event);
                        }, i * 800);
                    }
                }
            """)
            
            await asyncio.sleep(random.uniform(4, 8))
            
            # Étape 2: Page de connexion
            self.logger.info("🔑 Navigation vers la page de connexion...")
            await page.goto("https://myselogerpro.com/login", timeout=30000)
            await page.wait_for_load_state("networkidle")
            
            # Accepter cookies
            try:
                cookie_button = await page.query_selector("button:has-text('Accepter')")
                if cookie_button:
                    self.logger.info("🍪 Acceptation des cookies...")
                    await cookie_button.click()
                    await asyncio.sleep(random.uniform(1, 3))
            except:
                pass
            
            # Étape 3: Connexion avec stratégie validée (fast_fill)
            return await self.optimized_login(page)
            
        except Exception as e:
            self.logger.error(f"Erreur navigation: {e}")
            return False
    
    async def optimized_login(self, page):
        """Connexion optimisée basée sur la stratégie fast_fill validée"""
        
        try:
            self.logger.info("🔐 Recherche des champs de connexion...")
            
            # Trouver les champs
            email_field = await page.query_selector("input:first-of-type")
            password_field = await page.query_selector("input[type='password']")
            login_button = await page.query_selector("button:has-text('Me connecter')")
            
            if not email_field or not password_field or not login_button:
                self.logger.error("❌ Champs de connexion non trouvés")
                return False
            
            self.logger.info("✅ Champs trouvés")
            
            # Stratégie fast_fill validée (simulation gestionnaire de mots de passe)
            self.logger.info("⚡ Remplissage rapide (gestionnaire de mots de passe)")
            
            username = self.config['auth']['username']
            password = self.config['auth']['password']
            
            # Email - remplissage rapide
            await email_field.fill(username)
            await asyncio.sleep(random.uniform(0.4, 0.9))
            
            # Navigation par Tab (plus humain)
            await page.keyboard.press('Tab')
            self.logger.info("⌨️  Navigation par Tab")
            await asyncio.sleep(random.uniform(0.3, 0.7))
            
            # Mot de passe - remplissage rapide
            await password_field.fill(password)
            await asyncio.sleep(random.uniform(0.8, 1.5))
            
            # Clic sur le bouton avec variabilité
            self.logger.info("🖱️  Clic sur le bouton de connexion...")
            
            # Mouvement de souris naturel vers le bouton
            box = await login_button.bounding_box()
            if box:
                center_x = box["x"] + box["width"] / 2
                center_y = box["y"] + box["height"] / 2
                
                # Variation légère
                offset_x = random.uniform(-box["width"] * 0.15, box["width"] * 0.15)
                offset_y = random.uniform(-box["height"] * 0.15, box["height"] * 0.15)
                
                await page.mouse.move(center_x + offset_x, center_y + offset_y)
                await asyncio.sleep(random.uniform(0.3, 0.8))
                await page.mouse.click(center_x + offset_x, center_y + offset_y)
            else:
                await login_button.click()
            
            # Attendre la réponse
            self.logger.info("⏳ Attente de la réponse...")
            await page.wait_for_load_state("domcontentloaded", timeout=30000)
            await asyncio.sleep(3)
            
            # Vérifier le résultat
            current_url = page.url
            
            # Capture d'écran
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            await page.screenshot(path=f"screenshots/login_result_{timestamp}.png")
            
            if "login" in current_url:
                # Vérifier captcha
                captcha_present = await page.query_selector("iframe[src*='captcha']")
                if captcha_present:
                    self.logger.info("✅ Succès: Arrivé au captcha (pas de blocage)")
                    return True
                else:
                    self.logger.warning("⚠️  Toujours sur login sans captcha")
                    return False
            else:
                self.logger.info("✅ Succès: Connexion directe réussie")
                return True
                
        except Exception as e:
            self.logger.error(f"Erreur connexion: {e}")
            return False
    
    async def perform_renewals(self, page):
        """Effectue les renouvellements d'annonces"""
        
        try:
            self.logger.info("🔄 Début des renouvellements...")
            
            # Navigation vers le portefeuille
            await page.goto("https://myselogerpro.com/annonce/portefeuille", timeout=30000)
            await page.wait_for_load_state("networkidle")
            
            # Ici, vous pouvez ajouter la logique de renouvellement
            # basée sur votre code existant
            
            self.logger.info("✅ Renouvellements terminés")
            
        except Exception as e:
            self.logger.error(f"Erreur renouvellements: {e}")

def main():
    """Fonction principale"""
    
    print("🛡️  SeLogerPro - Version Optimisée Anti-Détection")
    print("=" * 55)
    print("✅ Basée sur les stratégies validées par les tests")
    print("✅ Taux de réussite amélioré")
    print("✅ Comportement humain optimisé")
    print()
    
    # Créer le dossier screenshots
    os.makedirs("screenshots", exist_ok=True)
    
    try:
        seloger = SeLogerOptimized()
        asyncio.run(seloger.run_optimized_automation())
        
        print("\n🎉 Automatisation terminée avec succès!")
        
    except KeyboardInterrupt:
        print("\n⏹️  Automatisation interrompue par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur: {e}")

if __name__ == "__main__":
    main()
