#!/usr/bin/env python3
"""
Script de test pour vérifier la configuration des proxies IProyal
"""

import asyncio
import json
import logging
import sys
from seloger_renewer import ProxyRotator

async def test_proxy_configuration():
    """Teste la configuration des proxies"""
    
    # Configuration du logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    try:
        # Charger la configuration
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        # Vérifier si les proxies sont activés
        if not config.get("proxy", {}).get("enabled", False):
            logger.error("Les proxies ne sont pas activés dans la configuration")
            return False
        
        proxy_config = config["proxy"]
        logger.info("Configuration proxy trouvée:")
        logger.info(f"  Hostname: {proxy_config['hostname']}")
        logger.info(f"  Port: {proxy_config['port']}")
        logger.info(f"  Username: {proxy_config['username']}")
        logger.info(f"  Pays disponibles: {proxy_config['countries']}")
        
        # Créer le gestionnaire de proxy
        proxy_rotator = ProxyRotator(proxy_config, logger)
        
        # Tester plusieurs proxies
        logger.info("\n=== Test des proxies ===")
        
        for i in range(3):
            logger.info(f"\nTest {i+1}/3:")
            
            # Obtenir un proxy
            proxy = await proxy_rotator.get_current_proxy()
            
            if proxy:
                logger.info(f"✓ Proxy obtenu: {proxy['country']} via {proxy['server']}")
                logger.info(f"  Username: {proxy['username']}")
            else:
                logger.warning("✗ Aucun proxy disponible")
            
            # Forcer une rotation pour le test suivant
            if i < 2:  # Ne pas forcer la rotation au dernier test
                await proxy_rotator.force_rotation()
        
        # Test de gestion des échecs
        logger.info("\n=== Test de gestion des échecs ===")
        
        current_proxy = await proxy_rotator.get_current_proxy()
        if current_proxy:
            logger.info(f"Proxy actuel: {current_proxy['country']}")
            
            # Simuler des échecs
            for i in range(proxy_rotator.max_failures_per_proxy + 1):
                proxy_rotator.report_proxy_failure(current_proxy)
                logger.info(f"Échec {i+1} signalé pour {current_proxy['country']}")
            
            # Vérifier si le proxy est marqué comme défaillant
            if proxy_rotator._proxy_has_too_many_failures(current_proxy):
                logger.info("✓ Proxy correctement marqué comme défaillant")
                
                # Obtenir un nouveau proxy
                new_proxy = await proxy_rotator.get_current_proxy()
                if new_proxy and new_proxy['country'] != current_proxy['country']:
                    logger.info(f"✓ Rotation automatique vers {new_proxy['country']}")
                else:
                    logger.warning("✗ Rotation automatique non effectuée")
            else:
                logger.warning("✗ Proxy non marqué comme défaillant")
        
        logger.info("\n=== Test terminé avec succès ===")
        return True
        
    except FileNotFoundError:
        logger.error("Fichier config.json non trouvé")
        return False
    except json.JSONDecodeError:
        logger.error("Erreur de format dans config.json")
        return False
    except Exception as e:
        logger.error(f"Erreur lors du test: {e}")
        return False

async def test_proxy_connectivity():
    """Teste la connectivité des proxies avec des tests détaillés"""

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)

    try:
        # Charger la configuration
        with open('config.json', 'r') as f:
            config = json.load(f)

        proxy_config = config["proxy"]
        proxy_rotator = ProxyRotator(proxy_config, logger)

        logger.info("=== Test de connectivité détaillé des proxies IProyal ===")
        logger.info(f"Configuration:")
        logger.info(f"  - Hostname: {proxy_config['hostname']}")
        logger.info(f"  - Port: {proxy_config['port']}")
        logger.info(f"  - Username: {proxy_config['username']}")
        logger.info(f"  - Password: {proxy_config['password'][:10]}...")

        # Test 1: Génération de configuration avec session
        logger.info(f"\n=== Test 1: Génération de configurations ===")
        for country in proxy_config["countries"][:3]:  # Tester les 3 premiers pays
            test_proxy = proxy_rotator._generate_proxy_config(country)
            logger.info(f"{country}:")
            logger.info(f"  - Username complet: {test_proxy['username']}")
            logger.info(f"  - Session ID: {test_proxy.get('session_id', 'N/A')}")
            logger.info(f"  - URL proxy: {test_proxy['full_proxy_url'][:50]}...")

        # Test 2: Test de connectivité pour chaque pays
        logger.info(f"\n=== Test 2: Connectivité par pays ===")
        working_countries = []
        failed_countries = []

        for country in proxy_config["countries"]:
            logger.info(f"\nTest de connectivité pour {country}:")

            # Générer une configuration pour ce pays
            test_proxy = proxy_rotator._generate_proxy_config(country)

            # Tester la connectivité
            is_working = await proxy_rotator._test_proxy(test_proxy)

            if is_working:
                logger.info(f"✅ {country}: Connectivité OK")
                working_countries.append(country)
            else:
                logger.warning(f"❌ {country}: Connectivité échouée")
                failed_countries.append(country)

        # Résumé des tests
        logger.info(f"\n=== Résumé des tests de connectivité ===")
        logger.info(f"Pays fonctionnels: {len(working_countries)}/{len(proxy_config['countries'])}")
        if working_countries:
            logger.info(f"  - Succès: {', '.join(working_countries)}")
        if failed_countries:
            logger.warning(f"  - Échecs: {', '.join(failed_countries)}")

        # Test 3: Test de rotation avec vérification IP
        if working_countries:
            logger.info(f"\n=== Test 3: Rotation et vérification IP ===")
            for i in range(min(3, len(working_countries))):
                await proxy_rotator.force_rotation()
                current_proxy = await proxy_rotator.get_current_proxy()

                if current_proxy:
                    logger.info(f"Rotation {i+1}: {current_proxy['country']} (Session: {current_proxy.get('session_id', 'N/A')})")

                    # Test de connectivité pour cette rotation
                    if await proxy_rotator.test_current_proxy_connectivity():
                        logger.info(f"  ✅ Connectivité confirmée")
                    else:
                        logger.warning(f"  ❌ Connectivité échouée après rotation")
                else:
                    logger.warning(f"Rotation {i+1}: Aucun proxy disponible")

        logger.info("\n=== Test de connectivité terminé ===")
        return len(working_countries) > 0

    except Exception as e:
        logger.error(f"Erreur lors du test de connectivité: {e}")
        return False

def main():
    """Fonction principale"""
    import argparse

    parser = argparse.ArgumentParser(description="Test de configuration des proxies IProyal")
    parser.add_argument("--connectivity", "-c", action="store_true",
                       help="Test de connectivité uniquement")
    parser.add_argument("--full", "-f", action="store_true",
                       help="Test complet (configuration + connectivité)")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Mode verbeux")

    args = parser.parse_args()

    # Configuration du niveau de logging
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    print("🔧 Test de configuration des proxies IProyal")
    print("=" * 50)

    success = True

    if args.connectivity:
        # Test de connectivité uniquement
        print("Mode: Test de connectivité uniquement")
        success = asyncio.run(test_proxy_connectivity())
    elif args.full:
        # Test complet
        print("Mode: Test complet (configuration + connectivité)")
        print("\n1️⃣ Test de configuration...")
        config_success = asyncio.run(test_proxy_configuration())

        print("\n2️⃣ Test de connectivité...")
        connectivity_success = asyncio.run(test_proxy_connectivity())

        success = config_success and connectivity_success
    else:
        # Test de configuration par défaut
        print("Mode: Test de configuration (utilisez --help pour plus d'options)")
        success = asyncio.run(test_proxy_configuration())

    print("\n" + "=" * 50)
    if success:
        print("✅ Tous les tests ont réussi!")
        print("\n💡 Conseils:")
        print("   - Votre configuration proxy IProyal est correcte")
        print("   - Vous pouvez maintenant lancer l'automatisation SeLogerPro")
        print("   - Utilisez --visible pour voir le navigateur en action")
        sys.exit(0)
    else:
        print("❌ Certains tests ont échoué!")
        print("\n🔧 Actions recommandées:")
        print("   - Vérifiez vos identifiants IProyal dans config.json")
        print("   - Assurez-vous que votre compte IProyal est actif")
        print("   - Testez la connectivité réseau")
        print("   - Contactez le support IProyal si le problème persiste")
        sys.exit(1)

if __name__ == "__main__":
    main()
