#!/usr/bin/env python3
"""
Script de test pour vérifier la configuration des proxies IProyal
"""

import asyncio
import json
import logging
import sys
from seloger_renewer import ProxyRotator

async def test_proxy_configuration():
    """Teste la configuration des proxies"""
    
    # Configuration du logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    try:
        # Charger la configuration
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        # Vérifier si les proxies sont activés
        if not config.get("proxy", {}).get("enabled", False):
            logger.error("Les proxies ne sont pas activés dans la configuration")
            return False
        
        proxy_config = config["proxy"]
        logger.info("Configuration proxy trouvée:")
        logger.info(f"  Hostname: {proxy_config['hostname']}")
        logger.info(f"  Port: {proxy_config['port']}")
        logger.info(f"  Username: {proxy_config['username']}")
        logger.info(f"  Pays disponibles: {proxy_config['countries']}")
        
        # Créer le gestionnaire de proxy
        proxy_rotator = ProxyRotator(proxy_config, logger)
        
        # Tester plusieurs proxies
        logger.info("\n=== Test des proxies ===")
        
        for i in range(3):
            logger.info(f"\nTest {i+1}/3:")
            
            # Obtenir un proxy
            proxy = await proxy_rotator.get_current_proxy()
            
            if proxy:
                logger.info(f"✓ Proxy obtenu: {proxy['country']} via {proxy['server']}")
                logger.info(f"  Username: {proxy['username']}")
            else:
                logger.warning("✗ Aucun proxy disponible")
            
            # Forcer une rotation pour le test suivant
            if i < 2:  # Ne pas forcer la rotation au dernier test
                await proxy_rotator.force_rotation()
        
        # Test de gestion des échecs
        logger.info("\n=== Test de gestion des échecs ===")
        
        current_proxy = await proxy_rotator.get_current_proxy()
        if current_proxy:
            logger.info(f"Proxy actuel: {current_proxy['country']}")
            
            # Simuler des échecs
            for i in range(proxy_rotator.max_failures_per_proxy + 1):
                proxy_rotator.report_proxy_failure(current_proxy)
                logger.info(f"Échec {i+1} signalé pour {current_proxy['country']}")
            
            # Vérifier si le proxy est marqué comme défaillant
            if proxy_rotator._proxy_has_too_many_failures(current_proxy):
                logger.info("✓ Proxy correctement marqué comme défaillant")
                
                # Obtenir un nouveau proxy
                new_proxy = await proxy_rotator.get_current_proxy()
                if new_proxy and new_proxy['country'] != current_proxy['country']:
                    logger.info(f"✓ Rotation automatique vers {new_proxy['country']}")
                else:
                    logger.warning("✗ Rotation automatique non effectuée")
            else:
                logger.warning("✗ Proxy non marqué comme défaillant")
        
        logger.info("\n=== Test terminé avec succès ===")
        return True
        
    except FileNotFoundError:
        logger.error("Fichier config.json non trouvé")
        return False
    except json.JSONDecodeError:
        logger.error("Erreur de format dans config.json")
        return False
    except Exception as e:
        logger.error(f"Erreur lors du test: {e}")
        return False

async def test_proxy_connectivity():
    """Teste la connectivité des proxies"""
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    logger = logging.getLogger(__name__)
    
    try:
        # Charger la configuration
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        proxy_config = config["proxy"]
        proxy_rotator = ProxyRotator(proxy_config, logger)
        
        logger.info("=== Test de connectivité des proxies ===")
        
        # Tester chaque pays
        for country in proxy_config["countries"]:
            logger.info(f"\nTest de connectivité pour {country}:")
            
            # Générer une configuration pour ce pays
            test_proxy = proxy_rotator._generate_proxy_config(country)
            
            # Tester la connectivité
            is_working = await proxy_rotator._test_proxy(test_proxy)
            
            if is_working:
                logger.info(f"✓ {country}: Connectivité OK")
            else:
                logger.warning(f"✗ {country}: Connectivité échouée")
        
        logger.info("\n=== Test de connectivité terminé ===")
        return True
        
    except Exception as e:
        logger.error(f"Erreur lors du test de connectivité: {e}")
        return False

def main():
    """Fonction principale"""
    if len(sys.argv) > 1 and sys.argv[1] == "--connectivity":
        # Test de connectivité uniquement
        success = asyncio.run(test_proxy_connectivity())
    else:
        # Test complet
        success = asyncio.run(test_proxy_configuration())
    
    if success:
        print("\n✓ Tous les tests ont réussi!")
        sys.exit(0)
    else:
        print("\n✗ Certains tests ont échoué!")
        sys.exit(1)

if __name__ == "__main__":
    main()
